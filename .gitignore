# Xcode
#
# gitignore 适用于iOS/Swift项目
#
## 构建产物
build/
DerivedData/
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

## 各种设置
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/

## 其他
*.moved-aside
*.xccheckout
*.xcscmblueprint
*.xcuserstate
.DS_Store

## 依赖管理工具
# CocoaPods
Pods/
# 取消注释下一行以保留Podfile.lock (推荐)
# Podfile.lock

# Carthage
Carthage/Build/

# Swift Package Manager
.build/
Package.pins
Package.resolved
*.xcodeproj
*.swiftpm
.swiftpm/

# Obj-C/Swift 特定文件
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# 项目特定的备份文件
*~
*.swp
*.swo

# 编译产物
*.o
*.LinkFileList
*.d

# App 打包目录
*.app.dSYM.zip
*.app.dSYM

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# 代码覆盖率文件
*.gcda
*.gcno

# Playgrounds
timeline.xctimeline
playground.xcworkspace

# 特定于macOS的文件
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# 特定于项目的文件
**/xcshareddata/WorkspaceSettings.xcsettings 