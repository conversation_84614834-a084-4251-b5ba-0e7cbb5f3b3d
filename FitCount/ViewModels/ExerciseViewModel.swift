/**
 * 运动视图模型
 *
 * 功能说明：
 * - 管理运动相关的业务逻辑
 * - 协调相机服务和计数服务
 * - 为UI层提供数据绑定
 *
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import Combine
import SwiftUI
import MediaPipeTasksVision
import AVFoundation

// MARK: - 运动视图模型
/**
 * 运动视图模型类
 *
 * 负责管理运动检测页面的所有业务逻辑
 */
class ExerciseViewModel: ObservableObject {

    // MARK: - Published Properties
    /// 当前运动类型
    @Published var currentExerciseType: ExerciseType?

    /// 当前计数
    @Published var currentCount: Int = 0

    /// 运动状态
    @Published var exerciseState: ExerciseState = .idle

    /// 是否显示设置面板
    @Published var showSettings: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 是否显示错误提示
    @Published var showError: Bool = false

    /// 当前角度信息（用于调试显示）
    @Published var currentAngle: Double?

    /// 姿态质量评分
    @Published var poseQuality: Double = 0.0

    /// 运动配置
    @Published var exerciseConfiguration: ExerciseConfiguration?

    // MARK: - Services
    private let cameraService: any CameraServiceProtocol
    private let countingService: any ExerciseCountingServiceProtocol
    private let poseLandmarkerService: PoseLandmarkerService

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Computed Properties
    /// 是否正在运动
    var isExercising: Bool {
        exerciseState == .detecting || exerciseState == .counting
    }

    /// 运动状态描述
    var exerciseStateDescription: String {
        switch exerciseState {
        case .idle:
            return "准备开始"
        case .detecting:
            return "检测中..."
        case .counting:
            return "计数中"
        case .paused:
            return "已暂停"
        case .completed:
            return "已完成"
        }
    }

    /// 当前运动的本地化名称
    var currentExerciseName: String {
        guard let exerciseType = currentExerciseType else {
            return "未选择运动"
        }
        return NSLocalizedString(exerciseType.rawValue, comment: "")
    }

    // MARK: - Initialization
    /**
     * 初始化运动视图模型
     *
     * - Parameter cameraService: 相机服务实例
     * - Parameter countingService: 计数服务实例
     * - Parameter poseLandmarkerService: 姿态检测服务实例
     */
    init(
        cameraService: any CameraServiceProtocol = CameraService(),
        countingService: any ExerciseCountingServiceProtocol = ExerciseCountingService(),
        poseLandmarkerService: PoseLandmarkerService
    ) {
        self.cameraService = cameraService
        self.countingService = countingService
        self.poseLandmarkerService = poseLandmarkerService

        setupBindings()
        setupPoseLandmarkerService()
    }

    // MARK: - Public Methods
    /**
     * 开始运动检测
     *
     * - Parameter exerciseType: 运动类型
     */
    func startExercise(_ exerciseType: ExerciseType) {
        DebugLogger.info("开始\(exerciseType.localizedName)检测")

        // 设置当前运动类型
        currentExerciseType = exerciseType

        // 创建运动配置
        let configuration = ExerciseConfiguration.defaultConfiguration(for: exerciseType)
        exerciseConfiguration = configuration

        // 开始计数服务
        countingService.startExercise(exerciseType, configuration: configuration)

        // 启动相机
        cameraService.startSession()
    }

    /**
     * 停止运动检测
     */
    func stopExercise() {
        DebugLogger.info("停止运动检测")

        countingService.stopExercise()
        cameraService.stopSession()

        currentExerciseType = nil
        exerciseConfiguration = nil
    }

    /**
     * 暂停运动检测
     */
    func pauseExercise() {
        DebugLogger.info("暂停运动检测")
        countingService.pauseExercise()
    }

    /**
     * 恢复运动检测
     */
    func resumeExercise() {
        DebugLogger.info("恢复运动检测")
        countingService.resumeExercise()
    }

    /**
     * 重置计数
     */
    func resetCount() {
        DebugLogger.info("重置计数")
        countingService.resetCount()
    }

    /**
     * 切换摄像头
     */
    func toggleCamera() {
        DebugLogger.info("切换摄像头")
        cameraService.toggleCamera()
    }

    /**
     * 更新运动配置
     *
     * - Parameter configuration: 新的运动配置
     */
    func updateConfiguration(_ configuration: ExerciseConfiguration) {
        exerciseConfiguration = configuration
        countingService.updateConfiguration(configuration)
        DebugLogger.info("更新运动配置")
    }

    /**
     * 显示错误信息
     *
     * - Parameter message: 错误信息
     */
    func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }

    /**
     * 获取相机预览层
     *
     * - Returns: 相机预览层
     */
    func getCameraPreviewLayer() -> AVCaptureVideoPreviewLayer? {
        return cameraService.previewLayer ?? cameraService.createPreviewLayer()
    }

    // MARK: - Private Methods
    /**
     * 设置数据绑定
     */
    private func setupBindings() {
        // 绑定计数服务的状态
        // 注意：这里需要根据实际的服务实现来调整绑定方式
        // 暂时注释掉，等服务实现完成后再启用
        /*
        countingService.objectWillChange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] in
                guard let self = self else { return }
                self.currentCount = self.countingService.currentCount
                self.exerciseState = self.countingService.exerciseState
            }
            .store(in: &cancellables)

        // 绑定相机服务的错误信息
        cameraService.objectWillChange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] in
                guard let self = self else { return }
                if let errorMessage = self.cameraService.errorMessage {
                    self.showErrorMessage(errorMessage)
                }
            }
            .store(in: &cancellables)
        */

        // 监听应用生命周期
        NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                self?.pauseExercise()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                if self?.exerciseState == .paused {
                    self?.resumeExercise()
                }
            }
            .store(in: &cancellables)
    }

    /**
     * 设置姿态检测服务
     */
    private func setupPoseLandmarkerService() {
        // 设置相机输出代理
        cameraService.setOutputDelegate(self)

        // 配置姿态检测服务
        // 这里需要根据实际的PoseLandmarkerService接口进行配置
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension ExerciseViewModel: AVCaptureVideoDataOutputSampleBufferDelegate {
    /**
     * 处理相机输出的视频帧
     *
     * - Parameter output: 输出对象
     * - Parameter sampleBuffer: 视频帧缓冲区
     * - Parameter connection: 连接对象
     */
    func captureOutput(
        _ output: AVCaptureOutput,
        didOutput sampleBuffer: CMSampleBuffer,
        from connection: AVCaptureConnection
    ) {
        // 只有在运动检测激活时才处理视频帧
        guard isExercising else { return }

        // 获取当前时间戳
        let currentTimeMs = Date().timeIntervalSince1970 * 1000

        // 获取设备方向
        let orientation = UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation)

        // 异步处理姿态检测
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.poseLandmarkerService.detectAsync(
                sampleBuffer: sampleBuffer,
                orientation: orientation,
                timeStamps: Int(currentTimeMs)
            )
        }
    }
}

// MARK: - PoseLandmarkerServiceLiveStreamDelegate
extension ExerciseViewModel: PoseLandmarkerServiceLiveStreamDelegate {
    /**
     * 处理姿态检测结果
     *
     * - Parameter poseLandmarkerService: 姿态检测服务
     * - Parameter result: 检测结果
     * - Parameter error: 错误信息
     */
    func poseLandmarkerService(
        _ poseLandmarkerService: PoseLandmarkerService,
        didFinishDetection result: ResultBundle?,
        error: Error?
    ) {
        // 处理错误
        if let error = error {
            DispatchQueue.main.async { [weak self] in
                self?.showErrorMessage("姿态检测错误: \(error.localizedDescription)")
            }
            return
        }

        // 传递结果给计数服务
        countingService.processPoseDetectionResult(result)

        // 更新UI显示的调试信息
        if let result = result,
           let poseLandmarkerResult = result.poseLandmarkerResults.first as? PoseLandmarkerResult,
           !poseLandmarkerResult.landmarks.isEmpty {

            let analysisResult = PoseAnalysisResult(from: PoseDetectionResult(from: result))

            DispatchQueue.main.async { [weak self] in
                self?.currentAngle = analysisResult.torsoAngle
                self?.poseQuality = analysisResult.poseQuality
            }
        }
    }
}
