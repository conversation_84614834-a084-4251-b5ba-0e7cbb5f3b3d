/**
 * 相机服务
 * 
 * 功能说明：
 * - 管理相机会话的生命周期
 * - 处理相机权限和配置
 * - 提供相机预览和数据输出
 * 
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import AVFoundation
import Combine

// MARK: - 相机服务协议
/**
 * 相机服务协议
 * 
 * 定义相机服务的基本接口
 */
protocol CameraServiceProtocol: ObservableObject {
    /// 相机会话
    var session: AVCaptureSession { get }
    
    /// 预览层
    var previewLayer: AVCaptureVideoPreviewLayer? { get }
    
    /// 是否正在运行
    var isRunning: Bool { get }
    
    /// 是否使用前置摄像头
    var isUsingFrontCamera: Bool { get }
    
    /// 错误信息
    var errorMessage: String? { get }
    
    /// 开始相机会话
    func startSession()
    
    /// 停止相机会话
    func stopSession()
    
    /// 切换前后摄像头
    func toggleCamera()
    
    /// 设置输出代理
    func setOutputDelegate(_ delegate: AVCaptureVideoDataOutputSampleBufferDelegate)
}

// MARK: - 相机配置状态
/**
 * 相机配置状态枚举
 */
enum CameraConfigurationStatus {
    case success                    // 配置成功
    case permissionDenied          // 权限被拒绝
    case configurationFailed       // 配置失败
    case sessionNotRunning         // 会话未运行
}

// MARK: - 相机服务实现
/**
 * 相机服务实现类
 * 
 * 负责管理相机的所有操作
 */
class CameraService: NSObject, ObservableObject, CameraServiceProtocol {
    
    // MARK: - Published Properties
    @Published var isRunning: Bool = false
    @Published var isUsingFrontCamera: Bool = false
    @Published var errorMessage: String?
    @Published var isSessionInterrupted: Bool = false
    
    // MARK: - Properties
    let session = AVCaptureSession()
    private(set) var previewLayer: AVCaptureVideoPreviewLayer?
    private let videoOutput = AVCaptureVideoDataOutput()
    
    // MARK: - Private Properties
    private let sessionQueue = DispatchQueue(label: "com.fitcount.camera.session")
    private let videoOutputQueue = DispatchQueue(label: "com.fitcount.camera.output")
    private var configurationStatus: CameraConfigurationStatus = .sessionNotRunning
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupSession()
        setupObservers()
    }
    
    deinit {
        removeObservers()
    }
    
    // MARK: - Public Methods
    /**
     * 开始相机会话
     */
    func startSession() {
        DebugLogger.info("开始启动相机会话")
        
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 检查权限
            self.checkCameraPermission { [weak self] granted in
                guard let self = self else { return }
                
                if granted {
                    self.configureSession()
                    
                    if !self.session.isRunning {
                        self.session.startRunning()
                        
                        DispatchQueue.main.async {
                            self.isRunning = self.session.isRunning
                            if self.isRunning {
                                DebugLogger.info("相机会话启动成功")
                            } else {
                                self.errorMessage = "相机会话启动失败"
                                DebugLogger.error("相机会话启动失败")
                            }
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        self.errorMessage = "相机权限被拒绝"
                        DebugLogger.error("相机权限被拒绝")
                    }
                }
            }
        }
    }
    
    /**
     * 停止相机会话
     */
    func stopSession() {
        DebugLogger.info("停止相机会话")
        
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if self.session.isRunning {
                self.session.stopRunning()
                
                DispatchQueue.main.async {
                    self.isRunning = false
                    DebugLogger.info("相机会话已停止")
                }
            }
        }
    }
    
    /**
     * 切换前后摄像头
     */
    func toggleCamera() {
        DebugLogger.info("切换摄像头")
        
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.session.beginConfiguration()
            
            // 移除当前输入
            if let currentInput = self.session.inputs.first as? AVCaptureDeviceInput {
                self.session.removeInput(currentInput)
                
                // 获取新的摄像头位置
                let newPosition: AVCaptureDevice.Position = currentInput.device.position == .back ? .front : .back
                
                // 创建新的输入
                if let newDevice = self.getCameraDevice(for: newPosition),
                   let newInput = try? AVCaptureDeviceInput(device: newDevice),
                   self.session.canAddInput(newInput) {
                    
                    self.session.addInput(newInput)
                    
                    DispatchQueue.main.async {
                        self.isUsingFrontCamera = newPosition == .front
                        DebugLogger.info("切换到\(newPosition == .front ? "前置" : "后置")摄像头")
                    }
                } else {
                    // 恢复原输入
                    self.session.addInput(currentInput)
                    
                    DispatchQueue.main.async {
                        self.errorMessage = "切换摄像头失败"
                        DebugLogger.error("切换摄像头失败")
                    }
                }
            }
            
            self.session.commitConfiguration()
        }
    }
    
    /**
     * 设置输出代理
     * 
     * - Parameter delegate: 视频数据输出代理
     */
    func setOutputDelegate(_ delegate: AVCaptureVideoDataOutputSampleBufferDelegate) {
        videoOutput.setSampleBufferDelegate(delegate, queue: videoOutputQueue)
    }
    
    /**
     * 创建预览层
     * 
     * - Returns: 配置好的预览层
     */
    func createPreviewLayer() -> AVCaptureVideoPreviewLayer {
        let layer = AVCaptureVideoPreviewLayer(session: session)
        layer.videoGravity = .resizeAspectFill
        layer.connection?.videoOrientation = .portrait
        self.previewLayer = layer
        return layer
    }
    
    // MARK: - Private Methods
    /**
     * 设置相机会话
     */
    private func setupSession() {
        session.sessionPreset = .high
    }
    
    /**
     * 配置相机会话
     */
    private func configureSession() {
        guard configurationStatus != .success else { return }
        
        session.beginConfiguration()
        
        // 添加视频输入
        if let videoDevice = getCameraDevice(for: .back),
           let videoInput = try? AVCaptureDeviceInput(device: videoDevice),
           session.canAddInput(videoInput) {
            
            session.addInput(videoInput)
            isUsingFrontCamera = false
        } else if let frontDevice = getCameraDevice(for: .front),
                  let frontInput = try? AVCaptureDeviceInput(device: frontDevice),
                  session.canAddInput(frontInput) {
            
            session.addInput(frontInput)
            isUsingFrontCamera = true
        } else {
            configurationStatus = .configurationFailed
            session.commitConfiguration()
            return
        }
        
        // 添加视频输出
        if session.canAddOutput(videoOutput) {
            session.addOutput(videoOutput)
            
            // 配置输出设置
            videoOutput.videoSettings = [
                kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA
            ]
            
            // 设置输出方向
            if let connection = videoOutput.connection(with: .video) {
                connection.videoOrientation = .portrait
                if connection.isVideoMirroringSupported {
                    connection.isVideoMirrored = isUsingFrontCamera
                }
            }
        } else {
            configurationStatus = .configurationFailed
            session.commitConfiguration()
            return
        }
        
        session.commitConfiguration()
        configurationStatus = .success
        
        DebugLogger.info("相机会话配置完成")
    }
    
    /**
     * 获取指定位置的摄像头设备
     * 
     * - Parameter position: 摄像头位置
     * - Returns: 摄像头设备，如果不存在则返回nil
     */
    private func getCameraDevice(for position: AVCaptureDevice.Position) -> AVCaptureDevice? {
        let deviceTypes: [AVCaptureDevice.DeviceType] = [
            .builtInWideAngleCamera,
            .builtInDualCamera,
            .builtInTrueDepthCamera
        ]
        
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: deviceTypes,
            mediaType: .video,
            position: position
        )
        
        return discoverySession.devices.first
    }
    
    /**
     * 检查相机权限
     * 
     * - Parameter completion: 权限检查完成回调
     */
    private func checkCameraPermission(completion: @escaping (Bool) -> Void) {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                completion(granted)
            }
        case .denied, .restricted:
            completion(false)
        @unknown default:
            completion(false)
        }
    }
    
    /**
     * 设置通知观察者
     */
    private func setupObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sessionWasInterrupted),
            name: .AVCaptureSessionWasInterrupted,
            object: session
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sessionInterruptionEnded),
            name: .AVCaptureSessionInterruptionEnded,
            object: session
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sessionRuntimeError),
            name: .AVCaptureSessionRuntimeError,
            object: session
        )
    }
    
    /**
     * 移除通知观察者
     */
    private func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Notification Handlers
    @objc private func sessionWasInterrupted(notification: NSNotification) {
        DebugLogger.warning("相机会话被中断")
        
        DispatchQueue.main.async { [weak self] in
            self?.isSessionInterrupted = true
        }
    }
    
    @objc private func sessionInterruptionEnded(notification: NSNotification) {
        DebugLogger.info("相机会话中断结束")
        
        DispatchQueue.main.async { [weak self] in
            self?.isSessionInterrupted = false
        }
    }
    
    @objc private func sessionRuntimeError(notification: NSNotification) {
        DebugLogger.error("相机会话运行时错误")
        
        DispatchQueue.main.async { [weak self] in
            self?.errorMessage = "相机运行时错误"
        }
    }
}
