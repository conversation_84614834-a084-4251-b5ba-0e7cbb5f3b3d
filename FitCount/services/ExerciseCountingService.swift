/**
 * 运动计数服务
 *
 * 功能说明：
 * - 管理不同类型的运动计数器
 * - 协调姿态检测和计数逻辑
 * - 提供统一的运动计数接口
 *
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import Combine
import MediaPipeTasksVision

// MARK: - 运动计数服务协议
/**
 * 运动计数服务协议
 *
 * 定义运动计数服务的基本接口
 */
protocol ExerciseCountingServiceProtocol: ObservableObject {
    /// 当前运动类型
    var currentExerciseType: ExerciseType? { get }

    /// 当前计数
    var currentCount: Int { get }

    /// 运动状态
    var exerciseState: ExerciseState { get }

    /// 是否正在计数
    var isActive: Bool { get }

    /// 开始运动计数
    func startExercise(_ exerciseType: ExerciseType, configuration: ExerciseConfiguration?)

    /// 停止运动计数
    func stopExercise()

    /// 暂停运动计数
    func pauseExercise()

    /// 恢复运动计数
    func resumeExercise()

    /// 重置计数
    func resetCount()

    /// 处理姿态检测结果
    func processPoseDetectionResult(_ result: ResultBundle?)

    /// 更新运动配置
    func updateConfiguration(_ configuration: ExerciseConfiguration)
}

// MARK: - 运动计数服务实现
/**
 * 运动计数服务实现类
 *
 * 负责管理运动计数的整个生命周期
 */
class ExerciseCountingService: ObservableObject, ExerciseCountingServiceProtocol {

    // MARK: - Published Properties
    @Published var currentCount: Int = 0
    @Published var exerciseState: ExerciseState = .idle
    @Published var currentExerciseType: ExerciseType?
    @Published var lastAnalysisResult: PoseAnalysisResult?

    // MARK: - Private Properties
    private var currentCounter: (any ExerciseCounterProtocol)?
    private var poseAnalyzer = PoseAnalyzer.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Computed Properties
    /// 是否正在计数
    var isActive: Bool {
        exerciseState == .detecting || exerciseState == .counting
    }

    // MARK: - Initialization
    init() {
        setupObservers()
    }

    // MARK: - Public Methods
    /**
     * 开始运动计数
     *
     * - Parameter exerciseType: 运动类型
     * - Parameter configuration: 运动配置（可选）
     */
    func startExercise(_ exerciseType: ExerciseType, configuration: ExerciseConfiguration? = nil) {
        // 停止当前运动（如果有）
        stopExercise()

        // 创建新的计数器
        currentCounter = createCounter(for: exerciseType, configuration: configuration)
        currentExerciseType = exerciseType

        // 开始计数
        currentCounter?.startCounting()

        // 设置观察者
        setupCounterObservers()

        DebugLogger.info("开始\(exerciseType.localizedName)计数")
    }

    /**
     * 停止运动计数
     */
    func stopExercise() {
        currentCounter?.stopCounting()
        currentCounter = nil
        currentExerciseType = nil
        exerciseState = .idle

        DebugLogger.info("停止运动计数")
    }

    /**
     * 暂停运动计数
     */
    func pauseExercise() {
        currentCounter?.pauseCounting()
        DebugLogger.info("暂停运动计数")
    }

    /**
     * 恢复运动计数
     */
    func resumeExercise() {
        currentCounter?.resumeCounting()
        DebugLogger.info("恢复运动计数")
    }

    /**
     * 重置计数
     */
    func resetCount() {
        currentCounter?.resetCount()
        currentCount = 0
        DebugLogger.info("重置计数")
    }

    /**
     * 处理姿态检测结果
     *
     * - Parameter result: MediaPipe检测结果
     */
    func processPoseDetectionResult(_ result: ResultBundle?) {
        guard let counter = currentCounter,
              counter.isActive else {
            return
        }

        // 分析姿态
        guard let analysisResult = poseAnalyzer.analyzePose(from: result) else {
            return
        }

        // 更新分析结果
        lastAnalysisResult = analysisResult

        // 传递给计数器处理
        counter.processPoseAnalysis(analysisResult)
    }

    /**
     * 获取当前运动配置
     *
     * - Returns: 当前运动配置，如果没有活动运动则返回nil
     */
    func getCurrentConfiguration() -> ExerciseConfiguration? {
        return currentCounter?.configuration
    }

    /**
     * 更新运动配置
     *
     * - Parameter configuration: 新的运动配置
     */
    func updateConfiguration(_ configuration: ExerciseConfiguration) {
        currentCounter?.configuration = configuration
        DebugLogger.info("更新运动配置")
    }

    // MARK: - Private Methods
    /**
     * 创建指定类型的计数器
     *
     * - Parameter exerciseType: 运动类型
     * - Parameter configuration: 运动配置
     * - Returns: 对应的计数器实例
     */
    private func createCounter(for exerciseType: ExerciseType, configuration: ExerciseConfiguration?) -> any ExerciseCounterProtocol {
        switch exerciseType {
        case .situp:
            return SitUpCounter(exerciseType: exerciseType, configuration: configuration)
        case .pullup:
            return PullUpCounter(exerciseType: exerciseType, configuration: configuration)
        case .pushup:
            return PushUpCounter(exerciseType: exerciseType, configuration: configuration)
        case .squat:
            return SquatCounter(exerciseType: exerciseType, configuration: configuration)
        case .plank:
            return PlankCounter(exerciseType: exerciseType, configuration: configuration)
        }
    }

    /**
     * 设置通用观察者
     */
    private func setupObservers() {
        // 监听应用状态变化
        NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                self?.pauseExercise()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                if self?.exerciseState == .paused {
                    self?.resumeExercise()
                }
            }
            .store(in: &cancellables)
    }

    /**
     * 设置计数器观察者
     */
    private func setupCounterObservers() {
        guard let counter = currentCounter else { return }

        // 观察计数变化
        // counter.objectWillChange
            .sink { [weak self] in
                DispatchQueue.main.async {
                    self?.currentCount = counter.currentCount
                    self?.exerciseState = counter.exerciseState
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - 扩展计数器实现
/**
 * 引体向上计数器
 */
class PullUpCounter: BaseExerciseCounter {
    private var isInUpPosition = false

    override func processSpecificExercise(_ analysis: PoseAnalysisResult) {
        guard let leftArmAngle = analysis.leftArmAngle,
              let rightArmAngle = analysis.rightArmAngle else { return }

        let avgArmAngle = (leftArmAngle + rightArmAngle) / 2.0
        let threshold = configuration.angleThreshold

        if !isInUpPosition && avgArmAngle < threshold {
            isInUpPosition = true
            exerciseState = .counting
        } else if isInUpPosition && avgArmAngle > threshold {
            isInUpPosition = false
            incrementCount()
        }
    }

    override func resetCount() {
        super.resetCount()
        isInUpPosition = false
    }
}

/**
 * 俯卧撑计数器
 */
class PushUpCounter: BaseExerciseCounter {
    private var isInDownPosition = false

    override func processSpecificExercise(_ analysis: PoseAnalysisResult) {
        guard let leftArmAngle = analysis.leftArmAngle,
              let rightArmAngle = analysis.rightArmAngle else { return }

        let avgArmAngle = (leftArmAngle + rightArmAngle) / 2.0
        let threshold = configuration.angleThreshold

        if !isInDownPosition && avgArmAngle < threshold {
            isInDownPosition = true
            exerciseState = .counting
        } else if isInDownPosition && avgArmAngle > threshold {
            isInDownPosition = false
            incrementCount()
        }
    }

    override func resetCount() {
        super.resetCount()
        isInDownPosition = false
    }
}

/**
 * 深蹲计数器
 */
class SquatCounter: BaseExerciseCounter {
    private var isInDownPosition = false

    override func processSpecificExercise(_ analysis: PoseAnalysisResult) {
        guard let leftLegAngle = analysis.leftLegAngle,
              let rightLegAngle = analysis.rightLegAngle else { return }

        let avgLegAngle = (leftLegAngle + rightLegAngle) / 2.0
        let threshold = configuration.angleThreshold

        if !isInDownPosition && avgLegAngle < threshold {
            isInDownPosition = true
            exerciseState = .counting
        } else if isInDownPosition && avgLegAngle > threshold {
            isInDownPosition = false
            incrementCount()
        }
    }

    override func resetCount() {
        super.resetCount()
        isInDownPosition = false
    }
}

/**
 * 平板支撑计数器（计时器）
 */
class PlankCounter: BaseExerciseCounter {
    private var startTime: Date?
    private var timer: Timer?

    override func processSpecificExercise(_ analysis: PoseAnalysisResult) {
        guard let stabilityScore = analysis.stabilityScore else { return }

        let threshold = Double(configuration.confidenceThreshold)

        if stabilityScore >= threshold {
            if startTime == nil {
                startTime = Date()
                startTimer()
                exerciseState = .counting
            }
        } else {
            stopTimer()
            startTime = nil
            exerciseState = .detecting
        }
    }

    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self, let startTime = self.startTime else { return }
            self.currentCount = Int(Date().timeIntervalSince(startTime))
        }
    }

    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    override func resetCount() {
        super.resetCount()
        stopTimer()
        startTime = nil
    }
}

// MARK: - 导入必要的框架
#if os(iOS)
import UIKit
#endif
