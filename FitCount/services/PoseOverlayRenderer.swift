/**
 * 人体关键点绘制渲染器
 *
 * 功能说明：
 * - 负责在相机预览上绘制人体关键点
 * - 处理坐标转换和缩放
 * - 提供可定制的绘制样式
 *
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import UIKit
import CoreGraphics
import MediaPipeTasksVision

// MARK: - 绘制样式配置
/**
 * 人体关键点绘制样式配置
 */
struct PoseRenderingStyle {
    /// 关键点半径
    let pointRadius: CGFloat

    /// 关键点颜色
    let pointColor: UIColor

    /// 关键点填充颜色
    let pointFillColor: UIColor

    /// 连接线宽度
    let lineWidth: CGFloat

    /// 连接线颜色
    let lineColor: UIColor

    /// 是否显示关键点
    let showPoints: Bool

    /// 是否显示连接线
    let showLines: Bool

    /// 是否显示关键点索引
    let showPointIndices: Bool

    /// 默认样式
    static let `default` = PoseRenderingStyle(
        pointRadius: 4.0,
        pointColor: .yellow,
        pointFillColor: .red,
        lineWidth: 2.0,
        lineColor: UIColor(red: 0, green: 127/255.0, blue: 139/255.0, alpha: 1),
        showPoints: true,
        showLines: true,
        showPointIndices: false
    )

    /// 简化样式（仅显示主要连接线）
    static let simplified = PoseRenderingStyle(
        pointRadius: 3.0,
        pointColor: .systemBlue,
        pointFillColor: .systemBlue,
        lineWidth: 1.5,
        lineColor: .systemBlue,
        showPoints: false,
        showLines: true,
        showPointIndices: false
    )
}

// MARK: - 人体关键点绘制渲染器
/**
 * 人体关键点绘制渲染器类
 *
 * 负责将检测到的人体关键点绘制到视图上
 */
class PoseOverlayRenderer {

    // MARK: - Properties
    /// 绘制样式
    var style: PoseRenderingStyle

    /// 使用的连接线定义
    var connections: [PoseConnection]

    // MARK: - Initialization
    /**
     * 初始化渲染器
     *
     * - Parameter style: 绘制样式
     * - Parameter useSimplifiedConnections: 是否使用简化的连接线
     */
    init(style: PoseRenderingStyle = .default, useSimplifiedConnections: Bool = false) {
        self.style = style
        self.connections = PoseConnections.connections
    }

    // MARK: - Public Methods
    /**
     * 在指定的图形上下文中绘制人体关键点
     *
     * - Parameter context: 图形上下文
     * - Parameter landmarks: 归一化关键点数组
     * - Parameter imageSize: 原始图像尺寸
     * - Parameter viewSize: 视图尺寸
     * - Parameter contentMode: 内容模式
     * - Parameter orientation: 图像方向
     */
    func drawPose(
        in context: CGContext,
        landmarks: [NormalizedLandmark],
        imageSize: CGSize,
        viewSize: CGSize,
        contentMode: UIView.ContentMode,
        orientation: UIImage.Orientation
    ) {
        guard !landmarks.isEmpty else { return }

        // 计算坐标转换参数
        let transform = calculateCoordinateTransform(
            imageSize: imageSize,
            viewSize: viewSize,
            contentMode: contentMode,
            orientation: orientation
        )

        // 转换关键点坐标
        let transformedPoints = transformLandmarks(landmarks, using: transform)

        // 绘制连接线
        if style.showLines {
            drawConnections(in: context, points: transformedPoints)
        }

        // 绘制关键点
        if style.showPoints {
            drawPoints(in: context, points: transformedPoints)
        }

        // 绘制关键点索引（调试用）
        if style.showPointIndices {
            drawPointIndices(in: context, points: transformedPoints)
        }
    }

    /**
     * 创建包含人体关键点的图像
     *
     * - Parameter landmarks: 归一化关键点数组
     * - Parameter imageSize: 图像尺寸
     * - Parameter backgroundColor: 背景颜色
     * - Returns: 包含关键点绘制的图像
     */
    func createPoseImage(
        landmarks: [NormalizedLandmark],
        imageSize: CGSize,
        backgroundColor: UIColor = .clear
    ) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(imageSize, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else {
            UIGraphicsEndImageContext()
            return nil
        }

        // 设置背景
        context.setFillColor(backgroundColor.cgColor)
        context.fill(CGRect(origin: .zero, size: imageSize))

        // 绘制关键点
        drawPose(
            in: context,
            landmarks: landmarks,
            imageSize: imageSize,
            viewSize: imageSize,
            contentMode: .scaleToFill,
            orientation: .up
        )

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return image
    }

    // MARK: - Private Methods
    /**
     * 计算坐标转换参数
     */
    private func calculateCoordinateTransform(
        imageSize: CGSize,
        viewSize: CGSize,
        contentMode: UIView.ContentMode,
        orientation: UIImage.Orientation
    ) -> CoordinateTransform {
        let widthScale = viewSize.width / imageSize.width
        let heightScale = viewSize.height / imageSize.height

        var scaleFactor: CGFloat
        switch contentMode {
        case .scaleAspectFill:
            scaleFactor = max(widthScale, heightScale)
        case .scaleAspectFit:
            scaleFactor = min(widthScale, heightScale)
        default:
            scaleFactor = 1.0
        }

        let scaledSize = CGSize(
            width: imageSize.width * scaleFactor,
            height: imageSize.height * scaleFactor
        )

        let xOffset = (viewSize.width - scaledSize.width) / 2
        let yOffset = (viewSize.height - scaledSize.height) / 2

        return CoordinateTransform(
            scaleFactor: scaleFactor,
            xOffset: xOffset,
            yOffset: yOffset,
            imageSize: imageSize,
            viewSize: viewSize,
            orientation: orientation
        )
    }

    /**
     * 转换关键点坐标
     */
    private func transformLandmarks(
        _ landmarks: [NormalizedLandmark],
        using transform: CoordinateTransform
    ) -> [CGPoint] {
        return landmarks.map { landmark in
            // 根据设备方向调整坐标
            var x = CGFloat(landmark.x)
            var y = CGFloat(landmark.y)

            // 处理设备方向的坐标转换
            switch transform.orientation {
            case .left:
                (x, y) = (y, 1 - x)
            case .right:
                (x, y) = (1 - y, x)
            case .up:
                (x, y) = (1 - y, x)
            default:
                (x, y) = (1 - y, x)
            }

            // 应用缩放和偏移
            return CGPoint(
                x: x * transform.imageSize.width * transform.scaleFactor + transform.xOffset,
                y: y * transform.imageSize.height * transform.scaleFactor + transform.yOffset
            )
        }
    }

    /**
     * 绘制连接线
     */
    private func drawConnections(in context: CGContext, points: [CGPoint]) {
        context.setStrokeColor(style.lineColor.cgColor)
        context.setLineWidth(style.lineWidth)

        for connection in connections {
            guard connection.start < points.count,
                  connection.end < points.count else { continue }

            let startPoint = points[connection.start]
            let endPoint = points[connection.end]

            context.move(to: startPoint)
            context.addLine(to: endPoint)
        }

        context.strokePath()
    }

    /**
     * 绘制关键点
     */
    private func drawPoints(in context: CGContext, points: [CGPoint]) {
        for point in points {
            let rect = CGRect(
                x: point.x - style.pointRadius / 2,
                y: point.y - style.pointRadius / 2,
                width: style.pointRadius,
                height: style.pointRadius
            )

            // 绘制填充圆
            context.setFillColor(style.pointFillColor.cgColor)
            context.fillEllipse(in: rect)

            // 绘制边框
            context.setStrokeColor(style.pointColor.cgColor)
            context.setLineWidth(1.0)
            context.strokeEllipse(in: rect)
        }
    }

    /**
     * 绘制关键点索引（调试用）
     */
    private func drawPointIndices(in context: CGContext, points: [CGPoint]) {
        let font = UIFont.systemFont(ofSize: 10)
        let textColor = UIColor.white

        for (index, point) in points.enumerated() {
            let text = "\(index)"
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: textColor
            ]

            let attributedText = NSAttributedString(string: text, attributes: attributes)
            let textSize = attributedText.size()

            let textRect = CGRect(
                x: point.x - textSize.width / 2,
                y: point.y - textSize.height / 2,
                width: textSize.width,
                height: textSize.height
            )

            attributedText.draw(in: textRect)
        }
    }
}

// MARK: - 坐标转换参数
/**
 * 坐标转换参数结构体
 */
private struct CoordinateTransform {
    let scaleFactor: CGFloat
    let xOffset: CGFloat
    let yOffset: CGFloat
    let imageSize: CGSize
    let viewSize: CGSize
    let orientation: UIImage.Orientation
}
