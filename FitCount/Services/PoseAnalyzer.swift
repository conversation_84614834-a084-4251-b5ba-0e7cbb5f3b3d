/**
 * 人体姿态分析器
 *
 * 功能说明：
 * - 分析MediaPipe检测到的人体关键点
 * - 计算各种角度和姿态参数
 * - 提供运动检测所需的数据
 *
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import CoreGraphics
import MediaPipeTasksVision

// MARK: - 姿态分析器
/**
 * 姿态分析器类
 *
 * 负责将原始的人体关键点数据转换为有用的姿态分析结果
 */
class PoseAnalyzer {

    // MARK: - Singleton
    static let shared = PoseAnalyzer()

    private init() {}

    // MARK: - Public Methods
    /**
     * 分析姿态检测结果
     *
     * - Parameter result: MediaPipe检测结果
     * - Returns: 姿态分析结果，如果检测失败则返回nil
     */
    func analyzePose(from result: ResultBundle?) -> PoseAnalysisResult? {
        guard let result = result else { return nil }

        let detectionResult = PoseDetectionResult(from: result)
        guard detectionResult.isValidPose else { return nil }

        return PoseAnalysisResult(from: detectionResult)
    }

    /**
     * 计算躯干角度
     *
     * 用于仰卧起坐检测，计算肩膀-髋部-膝盖的角度
     *
     * - Parameter landmarks: 归一化关键点数组
     * - Returns: 躯干角度（度），如果计算失败返回nil
     */
    func calculateTorsoAngle(from landmarks: [NormalizedLandmark]) -> Double? {
        guard landmarks.count > max(PoseLandmarkIndex.leftShoulder.rawValue,
                                   PoseLandmarkIndex.leftHip.rawValue,
                                   PoseLandmarkIndex.leftKnee.rawValue) else {
            return nil
        }

        let shoulder = landmarks[PoseLandmarkIndex.leftShoulder.rawValue]
        let hip = landmarks[PoseLandmarkIndex.leftHip.rawValue]
        let knee = landmarks[PoseLandmarkIndex.leftKnee.rawValue]

        return calculateAngle(
            point1: CGPoint(x: CGFloat(shoulder.x), y: CGFloat(shoulder.y)),
            vertex: CGPoint(x: CGFloat(hip.x), y: CGFloat(hip.y)),
            point2: CGPoint(x: CGFloat(knee.x), y: CGFloat(knee.y))
        )
    }

    /**
     * 计算手臂角度
     *
     * 用于引体向上和俯卧撑检测
     *
     * - Parameter landmarks: 归一化关键点数组
     * - Parameter isLeft: 是否计算左臂角度
     * - Returns: 手臂角度（度），如果计算失败返回nil
     */
    func calculateArmAngle(from landmarks: [NormalizedLandmark], isLeft: Bool = true) -> Double? {
        let shoulderIndex = isLeft ? PoseLandmarkIndex.leftShoulder.rawValue : PoseLandmarkIndex.rightShoulder.rawValue
        let elbowIndex = isLeft ? PoseLandmarkIndex.leftElbow.rawValue : PoseLandmarkIndex.rightElbow.rawValue
        let wristIndex = isLeft ? PoseLandmarkIndex.leftWrist.rawValue : PoseLandmarkIndex.rightWrist.rawValue

        guard landmarks.count > max(shoulderIndex, elbowIndex, wristIndex) else {
            return nil
        }

        let shoulder = landmarks[shoulderIndex]
        let elbow = landmarks[elbowIndex]
        let wrist = landmarks[wristIndex]

        return calculateAngle(
            point1: CGPoint(x: CGFloat(shoulder.x), y: CGFloat(shoulder.y)),
            vertex: CGPoint(x: CGFloat(elbow.x), y: CGFloat(elbow.y)),
            point2: CGPoint(x: CGFloat(wrist.x), y: CGFloat(wrist.y))
        )
    }

    /**
     * 计算腿部角度
     *
     * 用于深蹲检测
     *
     * - Parameter landmarks: 归一化关键点数组
     * - Parameter isLeft: 是否计算左腿角度
     * - Returns: 腿部角度（度），如果计算失败返回nil
     */
    func calculateLegAngle(from landmarks: [NormalizedLandmark], isLeft: Bool = true) -> Double? {
        let hipIndex = isLeft ? PoseLandmarkIndex.leftHip.rawValue : PoseLandmarkIndex.rightHip.rawValue
        let kneeIndex = isLeft ? PoseLandmarkIndex.leftKnee.rawValue : PoseLandmarkIndex.rightKnee.rawValue
        let ankleIndex = isLeft ? PoseLandmarkIndex.leftAnkle.rawValue : PoseLandmarkIndex.rightAnkle.rawValue

        guard landmarks.count > max(hipIndex, kneeIndex, ankleIndex) else {
            return nil
        }

        let hip = landmarks[hipIndex]
        let knee = landmarks[kneeIndex]
        let ankle = landmarks[ankleIndex]

        return calculateAngle(
            point1: CGPoint(x: CGFloat(hip.x), y: CGFloat(hip.y)),
            vertex: CGPoint(x: CGFloat(knee.x), y: CGFloat(knee.y)),
            point2: CGPoint(x: CGFloat(ankle.x), y: CGFloat(ankle.y))
        )
    }

    /**
     * 计算身体稳定性评分
     *
     * 用于平板支撑检测，基于关键点的可见性和一致性
     *
     * - Parameter landmarks: 归一化关键点数组
     * - Returns: 稳定性评分（0-1），1表示最稳定
     */
    func calculateStabilityScore(from landmarks: [NormalizedLandmark]) -> Double {
        // 计算关键点的平均可见性
        let visibilitySum = landmarks.reduce(Float(0)) { $0 + ($1.visibility?.floatValue ?? 0.0) }
        let averageVisibility = visibilitySum / Float(landmarks.count)

        // 计算身体主要部位的对称性
        let symmetryScore = calculateBodySymmetry(from: landmarks)

        // 综合评分
        return Double(averageVisibility) * symmetryScore
    }

    /**
     * 检测运动类型
     *
     * 基于当前姿态自动识别可能的运动类型
     *
     * - Parameter analysis: 姿态分析结果
     * - Returns: 可能的运动类型数组，按可能性排序
     */
    func detectExerciseType(from analysis: PoseAnalysisResult) -> [ExerciseType] {
        var possibleExercises: [(ExerciseType, Double)] = []

        // 检测仰卧起坐姿态
        if let torsoAngle = analysis.torsoAngle {
            let situpProbability = calculateSitupProbability(torsoAngle: torsoAngle)
            possibleExercises.append((.situp, situpProbability))
        }

        // 检测俯卧撑姿态
        if let leftArmAngle = analysis.leftArmAngle,
           let rightArmAngle = analysis.rightArmAngle {
            let pushupProbability = calculatePushupProbability(
                leftArmAngle: leftArmAngle,
                rightArmAngle: rightArmAngle
            )
            possibleExercises.append((.pushup, pushupProbability))
        }

        // 检测深蹲姿态
        if let leftLegAngle = analysis.leftLegAngle,
           let rightLegAngle = analysis.rightLegAngle {
            let squatProbability = calculateSquatProbability(
                leftLegAngle: leftLegAngle,
                rightLegAngle: rightLegAngle
            )
            possibleExercises.append((.squat, squatProbability))
        }

        // 检测平板支撑姿态
        if let stabilityScore = analysis.stabilityScore {
            let plankProbability = calculatePlankProbability(stabilityScore: stabilityScore)
            possibleExercises.append((.plank, plankProbability))
        }

        // 按概率排序并返回
        return possibleExercises
            .sorted { $0.1 > $1.1 }
            .map { $0.0 }
    }

    // MARK: - Private Methods
    /**
     * 计算三点角度
     *
     * - Parameter point1: 第一个点
     * - Parameter vertex: 顶点（角的顶点）
     * - Parameter point2: 第二个点
     * - Returns: 角度（度）
     */
    private func calculateAngle(point1: CGPoint, vertex: CGPoint, point2: CGPoint) -> Double {
        let vector1 = CGPoint(x: point1.x - vertex.x, y: point1.y - vertex.y)
        let vector2 = CGPoint(x: point2.x - vertex.x, y: point2.y - vertex.y)

        let dotProduct = vector1.x * vector2.x + vector1.y * vector2.y
        let magnitude1 = sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
        let magnitude2 = sqrt(vector2.x * vector2.x + vector2.y * vector2.y)

        guard magnitude1 > 0 && magnitude2 > 0 else { return 0 }

        let cosAngle = dotProduct / (magnitude1 * magnitude2)
        let clampedCosAngle = max(-1.0, min(1.0, cosAngle))
        let angleInRadians = acos(clampedCosAngle)

        return angleInRadians * 180.0 / .pi
    }

    /**
     * 计算身体对称性
     *
     * - Parameter landmarks: 归一化关键点数组
     * - Returns: 对称性评分（0-1）
     */
    private func calculateBodySymmetry(from landmarks: [NormalizedLandmark]) -> Double {
        guard landmarks.count >= 33 else { return 0 }

        // 计算左右肩膀的对称性
        let leftShoulder = landmarks[PoseLandmarkIndex.leftShoulder.rawValue]
        let rightShoulder = landmarks[PoseLandmarkIndex.rightShoulder.rawValue]
        let shoulderSymmetry = 1.0 - abs(leftShoulder.y - rightShoulder.y)

        // 计算左右髋部的对称性
        let leftHip = landmarks[PoseLandmarkIndex.leftHip.rawValue]
        let rightHip = landmarks[PoseLandmarkIndex.rightHip.rawValue]
        let hipSymmetry = 1.0 - abs(leftHip.y - rightHip.y)

        // 返回平均对称性
        return Double(shoulderSymmetry + hipSymmetry) / 2.0
    }

    /**
     * 计算仰卧起坐概率
     */
    private func calculateSitupProbability(torsoAngle: Double) -> Double {
        // 仰卧起坐的典型角度范围是90-180度
        if torsoAngle >= 90 && torsoAngle <= 180 {
            return 1.0 - abs(torsoAngle - 135) / 45.0
        }
        return 0.0
    }

    /**
     * 计算俯卧撑概率
     */
    private func calculatePushupProbability(leftArmAngle: Double, rightArmAngle: Double) -> Double {
        // 俯卧撑的典型手臂角度范围是90-180度
        let avgArmAngle = (leftArmAngle + rightArmAngle) / 2.0
        if avgArmAngle >= 90 && avgArmAngle <= 180 {
            return 1.0 - abs(avgArmAngle - 135) / 45.0
        }
        return 0.0
    }

    /**
     * 计算深蹲概率
     */
    private func calculateSquatProbability(leftLegAngle: Double, rightLegAngle: Double) -> Double {
        // 深蹲的典型腿部角度范围是60-180度
        let avgLegAngle = (leftLegAngle + rightLegAngle) / 2.0
        if avgLegAngle >= 60 && avgLegAngle <= 180 {
            return 1.0 - abs(avgLegAngle - 120) / 60.0
        }
        return 0.0
    }

    /**
     * 计算平板支撑概率
     */
    private func calculatePlankProbability(stabilityScore: Double) -> Double {
        // 平板支撑需要高稳定性
        return stabilityScore > 0.8 ? stabilityScore : 0.0
    }
}
