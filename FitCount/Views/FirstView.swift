import SwiftUI
import AVFoundation
import Foundation

// 本地化字符串键
extension LocalizedStringKey {
    // 运动类型
    static let exerciseTitle = LocalizedStringKey("exercise")
    static let exerciseSelectionDescription = LocalizedStringKey("exercise.selection.description")

    // 具体运动项目
    static let exerciseSitup = LocalizedStringKey("exercise.situp")
    static let exercisePullup = LocalizedStringKey("exercise.pullup")
    static let exercisePushup = LocalizedStringKey("exercise.pushup")
    static let exerciseSquat = LocalizedStringKey("exercise.squat")
    static let exercisePlank = LocalizedStringKey("exercise.plank")

    // 相机权限
    static let cameraPermissionTitle = LocalizedStringKey("camera.permission.title")
    static let cameraPermissionMessage = LocalizedStringKey("camera.permission.message")
    static let cameraPermissionOk = LocalizedStringKey("camera.permission.ok")
}

/**
 * 运动选择主视图
 *
 * 功能说明：
 * - 显示可选择的运动类型列表
 * - 处理相机权限检查
 * - 导航到运动检测详情页面
 */
struct FirstView: View {
    // MARK: - State Properties
    @State private var selectedExercise: String?
    @State private var isShowingDetail: Bool = false
    @State private var showCameraPermissionAlert = false

    // MARK: - Data
    /// 支持的运动类型列表
    let exercises = ["仰卧起坐", "引体向上", "俯卧撑", "深蹲", "平板支撑"]

    var body: some View {
        NavigationView {
            VStack(alignment: .center, spacing: 20) {
                Text(.exerciseTitle)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)

                Text(.exerciseSelectionDescription)
                    .font(.subheadline)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                ScrollView {
                    VStack(spacing: 15) {
                        ForEach(exercises, id: \.self) { exercise in
                            ExerciseButton(
                                exerciseType: exercise,
                                isSelected: selectedExercise == exercise
                            ) {
                                selectedExercise = exercise
                                checkCameraPermission()
                            }
                        }
                    }
                    .padding(.vertical)
                }

                Spacer()
            }
            .navigationBarHidden(true)
            .fullScreenCover(isPresented: $isShowingDetail) {
                NavigationView {
                    if let exerciseType = selectedExercise {
                        Text("运动检测页面: \(exerciseType)")
                    } else {
                        Text("未选择运动类型")
                    }
                }
            }
        }
        .alert(isPresented: $showCameraPermissionAlert) {
            Alert(
                title: Text(.cameraPermissionTitle),
                message: Text(.cameraPermissionMessage),
                dismissButton: .default(Text(.cameraPermissionOk))
            )
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }

    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: // The user has previously granted access to the camera
            isShowingDetail = true

        case .notDetermined: // The user has not yet been asked for camera access
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        isShowingDetail = true
                    } else {
                        showCameraPermissionAlert = true
                    }
                }
            }

        case .denied, .restricted: // The user can't grant access due to restrictions or has previously denied access
            showCameraPermissionAlert = true

        @unknown default:
            break
        }
    }
}

// MARK: - 运动按钮组件
/**
 * 运动选择按钮组件
 *
 * 显示运动类型的图标、名称和描述
 */
struct ExerciseButton: View {
    let exerciseType: ExerciseType
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                // 运动图标
                Image(systemName: exerciseType.iconName)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : .blue)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    // 运动名称
                    Text(exerciseType.localizedName)
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(isSelected ? .white : .primary)

                    // 运动描述
                    Text(exerciseType.description)
                        .font(.caption)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                // 选择指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.white)
                        .font(.title3)
                }
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(isSelected ? Color.blue : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 15)
                            .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FirstView_Previews: PreviewProvider {
    static var previews: some View {
        FirstView()
            .environment(\.locale, .init(identifier: "en"))
        FirstView()
            .environment(\.locale, .init(identifier: "zh-Hans"))
            .previewDisplayName("First View - Chinese")
    }
}
