/**
 * 运动检测详情视图（重构版本）
 * 
 * 功能说明：
 * - 使用MVVM架构模式
 * - 分离业务逻辑和UI逻辑
 * - 支持多种运动类型检测
 * - 提供实时的人体关键点可视化
 * 
 * 作者：FitCount Team
 * 创建时间：2024
 */

import SwiftUI
import AVFoundation
import MediaPipeTasksVision

// MARK: - 运动检测详情视图
/**
 * 运动检测详情视图
 * 
 * 重构后的主要运动检测界面，使用MVVM架构
 */
struct ExerciseDetailView: View {
    // MARK: - Environment
    @Environment(\.dismiss) var dismiss
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    
    // MARK: - Properties
    let exerciseType: ExerciseType
    
    // MARK: - View Model
    @StateObject private var viewModel: ExerciseViewModel
    
    // MARK: - State
    @State private var showSettings = false
    
    // MARK: - Initialization
    /**
     * 初始化运动检测详情视图
     * 
     * - Parameter exerciseType: 运动类型
     */
    init(exerciseType: ExerciseType) {
        self.exerciseType = exerciseType
        
        // 创建必要的服务实例
        let poseLandmarkerService = PoseLandmarkerService.liveStreamPoseLandmarkerService(
            modelPath: InferenceConfigurationManager.sharedInstance.model.modelPath,
            numPoses: InferenceConfigurationManager.sharedInstance.numPoses,
            minPoseDetectionConfidence: InferenceConfigurationManager.sharedInstance.minPoseDetectionConfidence,
            minPosePresenceConfidence: InferenceConfigurationManager.sharedInstance.minPosePresenceConfidence,
            minTrackingConfidence: InferenceConfigurationManager.sharedInstance.minTrackingConfidence,
            liveStreamDelegate: nil, // 将在视图模型中设置
            delegate: InferenceConfigurationManager.sharedInstance.delegate
        )
        
        // 初始化视图模型
        self._viewModel = StateObject(wrappedValue: ExerciseViewModel(
            cameraService: CameraService(),
            countingService: ExerciseCountingService(),
            poseLandmarkerService: poseLandmarkerService!
        ))
    }
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景：相机预览
                cameraPreviewLayer
                
                // 前景：UI控件
                VStack(spacing: 0) {
                    // 顶部控制栏
                    topControlBar
                    
                    Spacer()
                    
                    // 底部信息栏
                    bottomInfoBar
                }
                .edgesIgnoringSafeArea(.bottom)
                
                // 错误提示
                if viewModel.showError {
                    errorOverlay
                }
            }
            .ignoresSafeArea(.keyboard)
        }
        .navigationTitle(viewModel.currentExerciseName)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            toolbarContent
        }
        .sheet(isPresented: $showSettings) {
            settingsSheet
        }
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定") {
                viewModel.showError = false
            }
        } message: {
            Text(viewModel.errorMessage ?? "未知错误")
        }
        .onAppear {
            startExercise()
        }
        .onDisappear {
            stopExercise()
        }
    }
    
    // MARK: - View Components
    
    /// 相机预览层
    private var cameraPreviewLayer: some View {
        Group {
            if let previewLayer = viewModel.getCameraPreviewLayer() {
                CameraPreviewView(previewLayer: previewLayer)
                    .edgesIgnoringSafeArea(.all)
            } else {
                Color.black
                    .edgesIgnoringSafeArea(.all)
                    .overlay(
                        VStack(spacing: 20) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            
                            Text("正在初始化相机...")
                                .foregroundColor(.white)
                                .font(.title2)
                        }
                    )
            }
        }
    }
    
    /// 顶部控制栏
    private var topControlBar: some View {
        HStack {
            // 运动类型和状态
            VStack(alignment: .leading, spacing: 4) {
                Text(viewModel.currentExerciseName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(viewModel.exerciseStateDescription)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
            
            // 控制按钮
            HStack(spacing: 15) {
                // 切换摄像头按钮
                Button(action: {
                    viewModel.toggleCamera()
                }) {
                    Image(systemName: "camera.rotate.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
                
                // 暂停/恢复按钮
                Button(action: {
                    if viewModel.exerciseState == .paused {
                        viewModel.resumeExercise()
                    } else {
                        viewModel.pauseExercise()
                    }
                }) {
                    Image(systemName: viewModel.exerciseState == .paused ? "play.fill" : "pause.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.black.opacity(0.6), Color.clear]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    /// 底部信息栏
    private var bottomInfoBar: some View {
        VStack(spacing: 10) {
            // 计数显示
            HStack {
                Text("计数:")
                    .font(.title3)
                    .foregroundColor(.white)
                
                Text("\(viewModel.currentCount)")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .monospacedDigit()
                
                Spacer()
                
                // 重置按钮
                Button(action: {
                    viewModel.resetCount()
                }) {
                    Image(systemName: "arrow.counterclockwise")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            
            // 调试信息（开发模式）
            #if DEBUG
            if let angle = viewModel.currentAngle {
                HStack {
                    Text("角度:")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Text(String(format: "%.1f°", angle))
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .monospacedDigit()
                    
                    Spacer()
                    
                    Text("质量:")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Text(String(format: "%.2f", viewModel.poseQuality))
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .monospacedDigit()
                }
            }
            #endif
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.clear, Color.black.opacity(0.6)]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    /// 工具栏内容
    private var toolbarContent: some ToolbarContent {
        Group {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("返回") {
                    dismiss()
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showSettings = true
                }) {
                    Image(systemName: "slider.horizontal.3")
                }
            }
        }
    }
    
    /// 设置面板
    private var settingsSheet: some View {
        NavigationView {
            ExerciseSettingsView(
                exerciseType: exerciseType,
                configuration: viewModel.exerciseConfiguration ?? ExerciseConfiguration.defaultConfiguration(for: exerciseType)
            ) { newConfiguration in
                viewModel.updateConfiguration(newConfiguration)
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        showSettings = false
                    }
                }
            }
        }
    }
    
    /// 错误覆盖层
    private var errorOverlay: some View {
        Color.black.opacity(0.5)
            .edgesIgnoringSafeArea(.all)
            .overlay(
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.red)
                    
                    Text(viewModel.errorMessage ?? "未知错误")
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .padding()
                    
                    Button("重试") {
                        viewModel.showError = false
                        startExercise()
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .padding()
                .background(Color.black.opacity(0.8))
                .cornerRadius(15)
                .padding()
            )
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始运动检测
     */
    private func startExercise() {
        DebugLogger.info("开始\(exerciseType.localizedName)检测")
        viewModel.startExercise(exerciseType)
    }
    
    /**
     * 停止运动检测
     */
    private func stopExercise() {
        DebugLogger.info("停止运动检测")
        viewModel.stopExercise()
    }
}

// MARK: - 相机预览视图
/**
 * 相机预览视图
 * 
 * 用于在SwiftUI中显示AVCaptureVideoPreviewLayer
 */
struct CameraPreviewView: UIViewRepresentable {
    let previewLayer: AVCaptureVideoPreviewLayer
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = .black
        
        previewLayer.videoGravity = .resizeAspectFill
        view.layer.addSublayer(previewLayer)
        
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        DispatchQueue.main.async {
            previewLayer.frame = uiView.bounds
        }
    }
    
    static func dismantleUIView(_ uiView: UIView, coordinator: ()) {
        // 清理预览层
        uiView.layer.sublayers?.forEach { $0.removeFromSuperlayer() }
    }
}

// MARK: - 预览
struct ExerciseDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ExerciseDetailView(exerciseType: .situp)
        }
    }
}
