import SwiftUI
import AVFoundation
import Combine
import Foundation
import MediaPipeTasksVision
import CoreMedia

// TODO: 请将此 DebugLogger 类移到单独的文件中（/FitCount/Utils/DebugLogger.swift）
// 并使用 Xcode 的“添加文件到目标”功能将其加入到项目中
// File -> Add Files to "FitCount"... -> 选择 Utils/DebugLogger.swift 文件 -> Add
// 调试日志工具类 - 帮助我们记录关键事件

// MARK: - Pose Overlay Structures
/// A straight line for pose connections.
struct PoseLine {
    let from: CGPoint
    let to: CGPoint
}

/// This structure holds the display parameters for the pose overlay.
struct PoseOverlayData {
    let dots: [CGPoint]
    let lines: [PoseLine]
}

/// Pose connection definition
struct PoseConnection {
    let start: Int
    let end: Int
}

/// MediaPipe pose landmark connections
struct PoseConnections {
    static let connections: [PoseConnection] = [
        // Face connections
        PoseConnection(start: 0, end: 1),   // nose to left eye inner
        PoseConnection(start: 1, end: 2),   // left eye inner to left eye
        PoseConnection(start: 2, end: 3),   // left eye to left eye outer
        PoseConnection(start: 0, end: 4),   // nose to right eye inner
        PoseConnection(start: 4, end: 5),   // right eye inner to right eye
        PoseConnection(start: 5, end: 6),   // right eye to right eye outer
        PoseConnection(start: 3, end: 7),   // left eye outer to left ear
        PoseConnection(start: 6, end: 8),   // right eye outer to right ear
        PoseConnection(start: 9, end: 10),  // mouth left to mouth right

        // Upper body connections
        PoseConnection(start: 11, end: 12), // left shoulder to right shoulder
        PoseConnection(start: 11, end: 13), // left shoulder to left elbow
        PoseConnection(start: 13, end: 15), // left elbow to left wrist
        PoseConnection(start: 12, end: 14), // right shoulder to right elbow
        PoseConnection(start: 14, end: 16), // right elbow to right wrist

        // Hand connections
        PoseConnection(start: 15, end: 17), // left wrist to left pinky
        PoseConnection(start: 15, end: 19), // left wrist to left index
        PoseConnection(start: 15, end: 21), // left wrist to left thumb
        PoseConnection(start: 16, end: 18), // right wrist to right pinky
        PoseConnection(start: 16, end: 20), // right wrist to right index
        PoseConnection(start: 16, end: 22), // right wrist to right thumb

        // Torso connections
        PoseConnection(start: 11, end: 23), // left shoulder to left hip
        PoseConnection(start: 12, end: 24), // right shoulder to right hip
        PoseConnection(start: 23, end: 24), // left hip to right hip

        // Lower body connections
        PoseConnection(start: 23, end: 25), // left hip to left knee
        PoseConnection(start: 25, end: 27), // left knee to left ankle
        PoseConnection(start: 24, end: 26), // right hip to right knee
        PoseConnection(start: 26, end: 28), // right knee to right ankle

        // Foot connections
        PoseConnection(start: 27, end: 29), // left ankle to left heel
        PoseConnection(start: 27, end: 31), // left ankle to left foot index
        PoseConnection(start: 28, end: 30), // right ankle to right heel
        PoseConnection(start: 28, end: 32), // right ankle to right foot index
    ]
}

// MARK: - Pose Overlay View
/// Custom view to visualize the pose landmarks result on top of the camera preview.
class PoseOverlayView: UIView {

    var poseOverlayData: PoseOverlayData? {
        didSet {
            setNeedsDisplay()
        }
    }

    private var contentImageSize: CGSize = CGSize.zero
    var imageContentMode: UIView.ContentMode = .scaleAspectFit
    private var orientation = UIDeviceOrientation.portrait

    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = UIColor.clear
        isOpaque = false
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        backgroundColor = UIColor.clear
        isOpaque = false
    }

    func updateOverlay(
        poseOverlayData: PoseOverlayData?,
        inBoundsOfContentImageOfSize imageSize: CGSize,
        imageContentMode: UIView.ContentMode) {

        self.clear()
        self.contentImageSize = imageSize
        self.poseOverlayData = poseOverlayData
        self.imageContentMode = imageContentMode
        self.orientation = UIDevice.current.orientation
        self.setNeedsDisplay()
    }

    func clear() {
        poseOverlayData = nil
        contentImageSize = CGSize.zero
        imageContentMode = .scaleAspectFit
        orientation = UIDevice.current.orientation
        setNeedsDisplay()
    }

    override func draw(_ rect: CGRect) {
        guard let overlayData = poseOverlayData else { return }

        drawLines(overlayData.lines)
        drawDots(overlayData.dots)
    }

    private func drawDots(_ dots: [CGPoint]) {
        for dot in dots {
            let dotRect = CGRect(
                x: CGFloat(dot.x) - DefaultConstants.pointRadius / 2,
                y: CGFloat(dot.y) - DefaultConstants.pointRadius / 2,
                width: DefaultConstants.pointRadius,
                height: DefaultConstants.pointRadius)
            let path = UIBezierPath(ovalIn: dotRect)
            DefaultConstants.pointFillColor.setFill()
            DefaultConstants.pointColor.setStroke()
            path.stroke()
            path.fill()
        }
    }

    private func drawLines(_ lines: [PoseLine]) {
        let path = UIBezierPath()
        for line in lines {
            path.move(to: line.from)
            path.addLine(to: line.to)
        }
        path.lineWidth = DefaultConstants.lineWidth
        DefaultConstants.lineColor.setStroke()
        path.stroke()
    }

    // Helper function to calculate offsets and scale factor
    static func offsetsAndScaleFactor(
        forImageOfSize imageSize: CGSize,
        tobeDrawnInViewOfSize viewSize: CGSize,
        withContentMode contentMode: UIView.ContentMode)
    -> (xOffset: CGFloat, yOffset: CGFloat, scaleFactor: Double) {

        let widthScale = viewSize.width / imageSize.width
        let heightScale = viewSize.height / imageSize.height

        var scaleFactor = 0.0

        switch contentMode {
        case .scaleAspectFill:
            scaleFactor = max(widthScale, heightScale)
        case .scaleAspectFit:
            scaleFactor = min(widthScale, heightScale)
        default:
            scaleFactor = 1.0
        }

        let scaledSize = CGSize(
            width: imageSize.width * scaleFactor,
            height: imageSize.height * scaleFactor)
        let xOffset = (viewSize.width - scaledSize.width) / 2
        let yOffset = (viewSize.height - scaledSize.height) / 2

        return (xOffset, yOffset, scaleFactor)
    }

    // Helper to create pose overlay data from landmarks
    static func createPoseOverlayData(
        fromPoseLandmarks landmarks: [NormalizedLandmark],
        inferredOnImageOfSize originalImageSize: CGSize,
        overlayViewSize: CGSize,
        imageContentMode: UIView.ContentMode,
        andOrientation orientation: UIImage.Orientation) -> PoseOverlayData? {

        guard !landmarks.isEmpty else { return nil }

        let offsetsAndScaleFactor = PoseOverlayView.offsetsAndScaleFactor(
            forImageOfSize: originalImageSize,
            tobeDrawnInViewOfSize: overlayViewSize,
            withContentMode: imageContentMode)

        var transformedPoseLandmarks: [CGPoint]!

        // Fix coordinate transformation based on MediaPipe's coordinate system
        // MediaPipe returns normalized coordinates (0-1) where:
        // - (0,0) is top-left
        // - (1,1) is bottom-right
        //
        // CRITICAL FIX: The camera sensor is typically in landscape orientation,
        // but the display might be in portrait. We need to transform coordinates
        // to match the actual display orientation.
        switch orientation {
        case .left:
            // Landscape right: rotate coordinates
            transformedPoseLandmarks = landmarks.map({CGPoint(x: CGFloat($0.y), y: 1 - CGFloat($0.x))})
        case .right:
            // Landscape left: rotate coordinates
            transformedPoseLandmarks = landmarks.map({CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x))})
        case .up:
            // Portrait mode: Camera is typically landscape, so we need to rotate for portrait display
            // Try rotating 90 degrees counter-clockwise to fix the alignment issue
            transformedPoseLandmarks = landmarks.map({CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x))})
        default:
            // Default: try the same transformation as portrait
            transformedPoseLandmarks = landmarks.map({CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x))})
        }

        let dots: [CGPoint] = transformedPoseLandmarks.map({
            CGPoint(
                x: CGFloat($0.x) * originalImageSize.width * offsetsAndScaleFactor.scaleFactor + offsetsAndScaleFactor.xOffset,
                y: CGFloat($0.y) * originalImageSize.height * offsetsAndScaleFactor.scaleFactor + offsetsAndScaleFactor.yOffset
            )
        })

        // Create lines using MediaPipe pose connections
        let lines: [PoseLine] = PoseConnections.connections.map({ connection in
            let start = dots[connection.start]
            let end = dots[connection.end]
            return PoseLine(from: start, to: end)
        })

        return PoseOverlayData(dots: dots, lines: lines)
    }
}

// MARK: PoseLandmarkerServiceLiveStreamDelegate
extension CameraManager: PoseLandmarkerServiceLiveStreamDelegate {

    func poseLandmarkerService(
        _ poseLandmarkerService: PoseLandmarkerService,
        didFinishDetection result: ResultBundle?,
        error: Error?) {
            DebugLogger.info("poseLandmarkerService")

            // 处理人体姿态检测结果并绘制关键点
            guard let result = result,
                  let poseLandmarkerResult = result.poseLandmarkerResults.first as? PoseLandmarkerResult,
                  !poseLandmarkerResult.landmarks.isEmpty else {
                return
            }

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                // 获取第一个检测到的人体的关键点
                let landmarks = poseLandmarkerResult.landmarks[0]

                // 如果有 overlay view，更新绘制
                if let overlayView = self.poseOverlayView {
                    // 获取正确的图像尺寸和内容模式
                    // 对于实时相机预览，我们需要使用预览层的坐标系统
                    var imageSize = overlayView.bounds.size
                    var contentMode: UIView.ContentMode = .scaleAspectFill

                    // 如果有预览层，使用其设置
                    if let previewLayer = self.preview {
                        contentMode = previewLayer.videoGravity.contentMode

                        // 对于实时预览，使用预览层的实际尺寸作为参考
                        // 这样可以确保坐标变换正确对应到屏幕坐标
                        imageSize = previewLayer.bounds.size

                        // 如果预览层尺寸为空，则使用相机格式尺寸
                        if imageSize.width == 0 || imageSize.height == 0 {
                            if let videoInput = self.session.inputs.first as? AVCaptureDeviceInput {
                                let device = videoInput.device
                                let format = device.activeFormat
                                let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)

                                // 对于相机预览，通常是横向的，所以我们需要根据实际显示调整
                                imageSize = CGSize(width: Int(dimensions.width), height: Int(dimensions.height))
                            }
                        }
                    }

                    // 创建 pose overlay data
                    let poseOverlayData = PoseOverlayView.createPoseOverlayData(
                        fromPoseLandmarks: landmarks,
                        inferredOnImageOfSize: imageSize,
                        overlayViewSize: overlayView.bounds.size,
                        imageContentMode: contentMode,
                        andOrientation: UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation)
                    )

                    // 更新 overlay view
                    overlayView.updateOverlay(
                        poseOverlayData: poseOverlayData,
                        inBoundsOfContentImageOfSize: imageSize,
                        imageContentMode: contentMode
                    )
                }
            }
        }
}

/**
 AVCaptureVideoDataOutputSampleBufferDelegate
 */
extension CameraManager: AVCaptureVideoDataOutputSampleBufferDelegate {


    /** This method delegates the CVPixelBuffer of the frame seen by the camera currently.
     */
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard CMSampleBufferGetImageBuffer(sampleBuffer) != nil else { return }

//        DebugLogger.info("captureOutput invoke ")
        //    if (imageBufferSize == nil) {
        //      imageBufferSize = CGSize(width: CVPixelBufferGetHeight(imageBuffer), height: CVPixelBufferGetWidth(imageBuffer))
        //    }
        //      DebugLogger.info("captureOutput")
        //    delegate?.didOutput(sampleBuffer: sampleBuffer, orientation: UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation))
        //        poseLandmarkerService?.didOutput(sampleBuffer: sampleBuffer, orientation: UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation))

        let orientation =   UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation)

        let currentTimeMs = Date().timeIntervalSince1970 * 1000
        // Pass the pixel buffer to mediapipe
        backgroundQueue.async { [weak self] in
            self?.poseLandmarkerService?.detectAsync(
                sampleBuffer: sampleBuffer,
                orientation: orientation,
                timeStamps: Int(currentTimeMs))
        }
    }
}

// 相机管理器，处理摄像头相关操作
class CameraManager: NSObject,ObservableObject {
    @Published var session = AVCaptureSession()
    @Published var output = AVCaptureVideoDataOutput()
    @Published var preview: AVCaptureVideoPreviewLayer?
    @Published var isUsingFrontCamera = false
    @Published var sessionError: String? = nil
    @Published var isSessionRunning = false
    @Published var isSessionInterrupted = false
    @Published var debugState: String = "初始化中" // 用于UI显示的状态
    let sampleBufferQueue = DispatchQueue(label: "sampleBufferQueue")
    private let backgroundQueue = DispatchQueue(label: "com.google.mediapipe.cameraController.backgroundQueue")

    // Pose overlay view for drawing pose landmarks
    @Published var poseOverlayView: PoseOverlayView?

    // 监听相机会话状态的通知观察者
    private var sessionRunningObservation: NSKeyValueObservation?
    private var notificationCenter: NotificationCenter
    private var sessionQueue = DispatchQueue(label: "camera.session.queue")


    // MARK: CameraFeedServiceDelegate
    weak var delegate: CameraFeedServiceDelegate?
    private let poseLandmarkerServiceQueue = DispatchQueue(
        label: "com.google.mediapipe.cameraController.poseLandmarkerServiceQueue",
        attributes: .concurrent)

    // Queuing reads and writes to poseLandmarkerService using the Apple recommended way
    // as they can be read and written from multiple threads and can result in race conditions.
    private var _poseLandmarkerService: PoseLandmarkerService?
    private var poseLandmarkerService: PoseLandmarkerService? {
        get {
            poseLandmarkerServiceQueue.sync {
                return self._poseLandmarkerService
            }
        }
        set {
            poseLandmarkerServiceQueue.async(flags: .barrier) {
                self._poseLandmarkerService = newValue
            }
        }
    }

    private func clearPoseLandmarkerServiceOnSessionInterruption() {
        //      stopObserveConfigChanges()
        poseLandmarkerService = nil
    }

    @objc private func clearAndInitializePoseLandmarkerService() {
        poseLandmarkerService = nil
        poseLandmarkerService = PoseLandmarkerService
            .liveStreamPoseLandmarkerService(
                modelPath: InferenceConfigurationManager.sharedInstance.model.modelPath,
                numPoses: InferenceConfigurationManager.sharedInstance.numPoses,
                minPoseDetectionConfidence: InferenceConfigurationManager.sharedInstance.minPoseDetectionConfidence,
                minPosePresenceConfidence: InferenceConfigurationManager.sharedInstance.minPosePresenceConfidence,
                minTrackingConfidence: InferenceConfigurationManager.sharedInstance.minTrackingConfidence,
                liveStreamDelegate: self,
                delegate: InferenceConfigurationManager.sharedInstance.delegate)
    }
    override init() {
        DebugLogger.info("CameraManager初始化")
        self.notificationCenter = NotificationCenter.default
        super.init()

        // 创建 pose overlay view
        self.poseOverlayView = PoseOverlayView()

        self.setupObservers()
        clearAndInitializePoseLandmarkerService()
    }

    deinit {
        DebugLogger.info("CameraManager销毁")
        self.removeObservers()
    }

    // 设置监听各种相机会话状态的观察者
    private func setupObservers() {
        DebugLogger.debug("设置会话状态观察者")

        // 监听会话被中断
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionWasInterrupted),
            name: .AVCaptureSessionWasInterrupted,
            object: session
        )

        // 监听会话中断结束
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionInterruptionEnded),
            name: .AVCaptureSessionInterruptionEnded,
            object: session
        )

        // 监听会话运行错误
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionRuntimeError),
            name: .AVCaptureSessionRuntimeError,
            object: session
        )

        // 监听应用进入后台
        notificationCenter.addObserver(
            self,
            selector: #selector(applicationWillResignActive),
            name: UIScene.willDeactivateNotification,
            object: nil
        )

        // 监听应用回到前台
        notificationCenter.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: UIScene.didActivateNotification,
            object: nil
        )

        // 监听会话开始运行
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidStartRunning),
            name: .AVCaptureSessionDidStartRunning,
            object: session
        )

        // 监听会话停止运行
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidStopRunning),
            name: .AVCaptureSessionDidStopRunning,
            object: session
        )
    }

    // 移除所有观察者
    private func removeObservers() {
        DebugLogger.debug("移除会话状态观察者")
        notificationCenter.removeObserver(self)
        sessionRunningObservation?.invalidate()
        sessionRunningObservation = nil
    }

    // 相机会话被中断
    @objc private func sessionWasInterrupted(notification: NSNotification) {
        DebugLogger.warning("相机会话被中断")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionInterrupted = true
            self.debugState = "相机会话被中断"
        }

        // 判断中断原因
        if let userInfoValue = notification.userInfo?[AVCaptureSessionInterruptionReasonKey] as AnyObject?,
           let reasonIntegerValue = userInfoValue.integerValue,
           let reason = AVCaptureSession.InterruptionReason(rawValue: reasonIntegerValue) {
            DebugLogger.warning("相机会话被中断，原因: \(reason)")

            var reasonString = "未知原因"
            var showResumeButton = false

            switch reason {
            case .videoDeviceNotAvailableWithMultipleForegroundApps:
                reasonString = "多个前台应用使用摄像头"
                showResumeButton = false
            case .videoDeviceInUseByAnotherClient:
                reasonString = "摄像头被其他应用使用"
                showResumeButton = false
            case .videoDeviceNotAvailableDueToSystemPressure:
                reasonString = "系统压力导致摄像头不可用"
                showResumeButton = false
            case .audioDeviceInUseByAnotherClient:
                reasonString = "音频设备被其他应用使用"
                showResumeButton = false
            case .videoDeviceNotAvailableInBackground:
                // 在后台时摄像头不可用
                reasonString = "应用在后台时摄像头不可用"
                showResumeButton = true
            @unknown default:
                reasonString = "未知原因(\(reason.rawValue))"
                showResumeButton = false
            }

            DispatchQueue.main.async { [weak self] in
                self?.debugState = "相机中断: \(reasonString)"
            }

            if showResumeButton {
                // 在这里可以展示重新开始按钮或提示
                DebugLogger.debug("应该显示恢复按钮")
            }
        }
    }

    // 相机会话中断结束
    @objc private func sessionInterruptionEnded(notification: NSNotification) {
        DebugLogger.info("相机会话中断结束")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionInterrupted = false
            self.debugState = "相机会话恢复"

            // 如果会话不在运行，尝试重新启动
            if !self.session.isRunning {
                DebugLogger.debug("中断结束后尝试重新启动会话")
                self.startSession()
            }
        }
    }

    // 相机会话运行错误
    @objc private func sessionRuntimeError(notification: NSNotification) {
        guard let error = notification.userInfo?[AVCaptureSessionErrorKey] as? AVError else {
            DebugLogger.error("相机会话运行错误，但无法获取错误详情")
            return
        }

        DebugLogger.error("相机会话运行错误: \(error.localizedDescription) (Code: \(error.code.rawValue))")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.sessionError = error.localizedDescription
            self.debugState = "相机错误: \(error.localizedDescription)"
        }

        // 根据错误类型进行不同处理
        if error.code == .mediaServicesWereReset {
            DebugLogger.info("媒体服务被重置，尝试重新建立会话")
            // 媒体服务被重置，可以尝试重新建立会话
            sessionQueue.async { [weak self] in
                guard let self = self else { return }
                self.stopSession()
                self.setupAndStartSession()
            }
        } else {
            // 其他错误，暂时不做特殊处理
            DebugLogger.warning("未处理的相机错误: \(error.code)")
        }
    }

    // 会话开始运行通知
    @objc private func sessionDidStartRunning(notification: NSNotification) {
        DebugLogger.info("收到会话开始运行通知")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionRunning = true
            self.debugState = "相机会话运行中"
        }
    }

    // 会话停止运行通知
    @objc private func sessionDidStopRunning(notification: NSNotification) {
        DebugLogger.info("收到会话停止运行通知")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionRunning = false
            self.debugState = "相机会话已停止"
        }
    }

    // 应用进入后台
    @objc private func applicationWillResignActive(notification: NSNotification) {
        DebugLogger.info("应用进入后台")
        if isSessionRunning {
            DebugLogger.debug("应用进入后台，停止相机会话")
            stopSession()
        }
    }

    // 应用回到前台
    @objc private func applicationDidBecomeActive(notification: NSNotification) {
        DebugLogger.info("应用回到前台")
        if !isSessionRunning && !isSessionInterrupted {
            DebugLogger.debug("应用回到前台，尝试启动相机会话")
            setupAndStartSession()
        }
    }

    // 设置并启动相机会话（完整流程）
    func setupAndStartSession() {
        DebugLogger.info("开始设置并启动相机")

        // 更新UI状态
        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在初始化相机..."
        }

        sessionQueue.async { [weak self] in
            guard let self = self else {
                DebugLogger.error("setupAndStartSession: self已释放")
                return
            }

            DebugLogger.debug("在sessionQueue中开始设置相机")

            // 检查当前会话状态
            if self.session.isRunning {
                DebugLogger.warning("会话已经在运行中，先停止它")
                self.session.stopRunning()
            }

            self.session.beginConfiguration()
            DebugLogger.debug("会话配置开始")

            // 检查相机权限
            let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
            DebugLogger.debug("相机权限状态: \(authStatus.rawValue)")

            switch authStatus {
            case .authorized:
                DebugLogger.info("已获得相机权限")
                // 继续设置相机
                self.configureSession()

            case .notDetermined:
                DebugLogger.info("相机权限未确定，请求权限")

                // 更新UI状态
                DispatchQueue.main.async {
                    self.debugState = "请求相机权限..."
                }

                // 请求权限（这会显示系统对话框）
                AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                    guard let self = self else { return }

                    if granted {
                        DebugLogger.info("用户授予了相机权限")
                        DispatchQueue.main.async {
                            self.debugState = "已获得相机权限，重新初始化..."
                        }
                        // 权限获取后重新调用设置
                        self.setupAndStartSession()
                    } else {
                        DebugLogger.error("用户拒绝了相机权限")
                        DispatchQueue.main.async {
                            self.sessionError = "相机权限被拒绝"
                            self.debugState = "相机权限被拒绝"
                        }
                        self.session.commitConfiguration()
                    }
                }
                return

            case .denied:
                DebugLogger.error("相机权限被拒绝")
                DispatchQueue.main.async {
                    self.sessionError = "相机权限被拒绝，请在设置中允许访问相机"
                    self.debugState = "相机权限被拒绝"
                }
                self.session.commitConfiguration()
                return

            case .restricted:
                DebugLogger.error("相机访问被限制")
                DispatchQueue.main.async {
                    self.sessionError = "相机访问受到限制，可能因为家长控制等原因"
                    self.debugState = "相机访问受限"
                }
                self.session.commitConfiguration()
                return

            @unknown default:
                DebugLogger.error("未知的相机权限状态")
                DispatchQueue.main.async {
                    self.sessionError = "未知的相机权限状态"
                    self.debugState = "权限状态异常"
                }
                self.session.commitConfiguration()
                return
            }
        }
    }

    // 配置相机会话（内部方法）
    private func configureSession() {
        DebugLogger.debug("开始配置相机会话")

        // 清除现有的输入和输出
        DebugLogger.debug("移除现有的输入和输出")
        for input in session.inputs {
            session.removeInput(input)
        }

        for output in session.outputs {
            session.removeOutput(output)
        }

        // 1. 设置摄像头输入
        DebugLogger.debug("设置摄像头输入，当前选择\(isUsingFrontCamera ? "前置" : "后置")摄像头")
        let position: AVCaptureDevice.Position = isUsingFrontCamera ? .front : .back

        // 获取摄像头设备
        guard let videoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: position) else {
            let errorMsg = "无法获取\(position == .front ? "前置" : "后置")摄像头设备"
            DebugLogger.error(errorMsg)

            // 尝试获取任何可用的摄像头设备
            if let anyCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .unspecified) {
                DebugLogger.info("找到替代摄像头设备: \(anyCamera.localizedName)")
                configureCameraInput(anyCamera)
            } else {
                DebugLogger.error("设备上没有可用的摄像头")
                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
                session.commitConfiguration()
            }
            return
        }

        DebugLogger.info("成功获取摄像头设备: \(videoDevice.localizedName)")
        configureCameraInput(videoDevice)
    }

    // 配置摄像头输入（辅助方法）
    private func configureCameraInput(_ videoDevice: AVCaptureDevice) {
        do {
            // 创建设备输入
            let videoDeviceInput = try AVCaptureDeviceInput(device: videoDevice)

            // 检查是否可以添加到会话
            if session.canAddInput(videoDeviceInput) {
                session.addInput(videoDeviceInput)
                DebugLogger.debug("成功添加视频输入到会话")
                configureVideoOutput() // 继续配置输出
            } else {
                let errorMsg = "无法将摄像头输入添加到会话"
                DebugLogger.error(errorMsg)
                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
                session.commitConfiguration()
            }
        } catch {
            let errorMsg = "创建摄像头输入时发生错误: \(error.localizedDescription)"
            DebugLogger.error(errorMsg)
            DispatchQueue.main.async { [weak self] in
                self?.sessionError = errorMsg
                self?.debugState = errorMsg
            }
            session.commitConfiguration()
        }
    }

    // 配置视频输出（辅助方法）
    private func configureVideoOutput() {
        DebugLogger.debug("配置视频输出")

        // 添加视频输出
        if session.canAddOutput(output) {
            session.addOutput(output)
            // 配置输出格式
            output.videoSettings =  [ String(kCVPixelBufferPixelFormatTypeKey) : kCMPixelFormat_32BGRA]
            output.alwaysDiscardsLateVideoFrames = true
            output.setSampleBufferDelegate(self, queue: sampleBufferQueue)
            DebugLogger.debug("成功添加视频输出到会话")
        } else {
            DebugLogger.warning("无法将视频输出添加到会话（这可能不是致命错误）")
        }

        // 创建预览层并完成配置
        finalizeConfiguration()
    }

    // 完成会话配置（辅助方法）
    private func finalizeConfiguration() {
        DebugLogger.debug("完成会话配置")

        // 创建预览层
        let newPreviewLayer = AVCaptureVideoPreviewLayer(session: session)
        newPreviewLayer.videoGravity = .resizeAspectFill

        DebugLogger.debug("创建了新的预览层，关联到会话")

        // 提交会话配置
        DebugLogger.debug("提交会话配置")
        session.commitConfiguration()

        // 在主线程更新UI状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            DebugLogger.debug("在主线程更新UI状态")

            // 发布预览层
            self.preview = newPreviewLayer
            self.debugState = "相机初始化完成，准备启动"

            // 如果预览层创建成功，在延迟短暂时间后启动会话
            // 这个延迟可以确保SwiftUI视图有足够时间将预览层添加到其视图层次结构中
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }

                // 如果会话没有运行且没有被中断，则启动它
                if !self.session.isRunning && !self.isSessionInterrupted {
                    DebugLogger.debug("延迟后开始启动相机会话")
                    self.debugState = "正在启动相机..."

                    // 在sessionQueue上启动会话
                    self.sessionQueue.async { [weak self] in
                        guard let self = self else { return }

                        // 启动前检查会话状态
                        if self.session.isRunning {
                            DebugLogger.warning("会话已经在运行，无需重复启动")
                            return
                        }

                        DebugLogger.debug("开始启动摄像头会话")
                        self.session.startRunning()

                        // 验证会话是否成功启动
                        DispatchQueue.main.async { [weak self] in
                            guard let self = self else { return }
                            self.isSessionRunning = self.session.isRunning

                            if self.isSessionRunning {
                                DebugLogger.info("摄像头会话成功启动")
                                self.debugState = "相机已启动"
                            } else {
                                let errorMsg = "摄像头会话启动失败"
                                DebugLogger.error(errorMsg)
                                self.sessionError = errorMsg
                                self.debugState = errorMsg
                            }
                        }
                    }
                } else {
                    if self.session.isRunning {
                        DebugLogger.warning("会话已经在运行中")
                    }
                    if self.isSessionInterrupted {
                        DebugLogger.warning("会话当前被中断，无法启动")
                    }
                }
            }
        }
    }

    // 切换前后摄像头
    func toggleCamera() {
        DebugLogger.info("切换前后摄像头")

        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在切换摄像头..."
        }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            DebugLogger.debug("在sessionQueue中开始切换摄像头")

            // 开始配置
            self.session.beginConfiguration()

            // 获取当前输入
            guard let currentInput = self.session.inputs.first as? AVCaptureDeviceInput else {
                DebugLogger.error("无法获取当前摄像头输入")
                self.session.commitConfiguration()

                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = "无法获取当前摄像头，切换失败"
                    self?.debugState = "切换摄像头失败"
                }
                return
            }

            // 获取当前摄像头位置
            let currentPosition = currentInput.device.position
            DebugLogger.debug("当前摄像头位置: \(currentPosition == .front ? "前置" : "后置")")

            // 确定新的摄像头位置
            let newPosition: AVCaptureDevice.Position = (currentPosition == .front) ? .back : .front
            DebugLogger.debug("目标摄像头位置: \(newPosition == .front ? "前置" : "后置")")

            // 移除当前输入
            self.session.removeInput(currentInput)
            DebugLogger.debug("已移除当前摄像头输入")

            // 查找新摄像头设备
            guard let newVideoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: newPosition) else {
                let errorMsg = "无法找到\(newPosition == .front ? "前置" : "后置")摄像头"
                DebugLogger.error(errorMsg)

                // 尝试恢复原始摄像头
                if self.session.canAddInput(currentInput) {
                    self.session.addInput(currentInput)
                    DebugLogger.debug("恢复使用原始摄像头")
                }

                self.session.commitConfiguration()

                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
                return
            }

            // 创建新的输入
            do {
                let newVideoInput = try AVCaptureDeviceInput(device: newVideoDevice)

                // 检查是否可以添加
                if self.session.canAddInput(newVideoInput) {
                    // 添加新的输入
                    self.session.addInput(newVideoInput)
                    DebugLogger.debug("成功添加新摄像头输入")

                    // 更新UI状态（在提交配置后）
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.isUsingFrontCamera = (newPosition == .front)
                        self.debugState = "已切换到\(newPosition == .front ? "前置" : "后置")摄像头"
                    }
                } else {
                    let errorMsg = "无法添加\(newPosition == .front ? "前置" : "后置")摄像头到会话"
                    DebugLogger.error(errorMsg)

                    // 尝试恢复原始摄像头
                    if self.session.canAddInput(currentInput) {
                        self.session.addInput(currentInput)
                        DebugLogger.debug("恢复使用原始摄像头")
                    }

                    DispatchQueue.main.async { [weak self] in
                        self?.sessionError = errorMsg
                        self?.debugState = errorMsg
                    }
                }
            } catch {
                let errorMsg = "创建\(newPosition == .front ? "前置" : "后置")摄像头输入时出错: \(error.localizedDescription)"
                DebugLogger.error(errorMsg)

                // 尝试恢复原始摄像头
                if self.session.canAddInput(currentInput) {
                    self.session.addInput(currentInput)
                    DebugLogger.debug("恢复使用原始摄像头")
                }

                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
            }

            // 提交配置
            self.session.commitConfiguration()
            DebugLogger.debug("摄像头切换配置已提交")
        }
    }

    // 开始相机会话
    func startSession() {
        DebugLogger.info("开始启动相机会话")

        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在启动相机..."
        }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            if self.session.isRunning {
                DebugLogger.warning("会话已经在运行中，无需重新启动")

                DispatchQueue.main.async { [weak self] in
                    self?.debugState = "相机已在运行中"
                }
                return
            }

            if self.isSessionInterrupted {
                DebugLogger.warning("会话当前被中断，无法启动")

                DispatchQueue.main.async { [weak self] in
                    self?.debugState = "相机被中断，无法启动"
                }
                return
            }

            DebugLogger.debug("在sessionQueue中启动会话")
            self.session.startRunning()

            // 检查启动是否成功
            let isRunning = self.session.isRunning
            DebugLogger.debug("会话启动结果: \(isRunning ? "成功" : "失败")")

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.isSessionRunning = isRunning

                if isRunning {
                    self.debugState = "相机已启动"
                } else {
                    let errorMsg = "相机会话无法启动"
                    self.sessionError = errorMsg
                    self.debugState = errorMsg
                }
            }
        }
    }

    // 停止相机会话
    func stopSession() {
        DebugLogger.info("停止相机会话")

        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在停止相机..."
        }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            if !self.session.isRunning {
                DebugLogger.warning("会话已经停止，无需再次停止")

                DispatchQueue.main.async { [weak self] in
                    self?.debugState = "相机已停止"
                }
                return
            }

            DebugLogger.debug("在sessionQueue中停止会话")
            self.session.stopRunning()

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.isSessionRunning = false
                self.debugState = "相机已停止"
            }
        }
    }

    // 检查并打印相机状态（调试方法）
    func checkCameraStatus() {
        let debugInfo = """
        ======= 相机状态检查 =======
        会话正在运行: \(session.isRunning)
        isSessionRunning: \(isSessionRunning)
        会话被中断: \(isSessionInterrupted)
        前置摄像头: \(isUsingFrontCamera)
        预览层: \(preview != nil ? "已创建" : "未创建")
        错误信息: \(sessionError ?? "无")
        当前状态: \(debugState)
        输入数量: \(session.inputs.count)
        输出数量: \(session.outputs.count)
        ============================
        """

        DebugLogger.info(debugInfo)
    }
}

// 预览视图 - 用于显示相机预览的自定义UIView
class CameraPreviewView: UIView {
    // 存储预览层引用
    var previewLayer: AVCaptureVideoPreviewLayer? {
        didSet {
            DebugLogger.debug("设置预览层")

            // 移除旧层
            if let oldLayer = oldValue {
                DebugLogger.debug("移除旧的预览层")
                oldLayer.removeFromSuperlayer()
            }

            // 设置新层
            if let newLayer = previewLayer {
                DebugLogger.debug("添加新的预览层到视图")

                // 配置预览层属性
                newLayer.videoGravity = .resizeAspectFill
                layer.addSublayer(newLayer)

                // 设置预览层框架以填充视图
                CATransaction.begin()
                CATransaction.setDisableActions(true) // 禁用动画
                newLayer.frame = bounds
                CATransaction.commit()

                // 检查预览层会话是否设置
                if let session = newLayer.session {
                    DebugLogger.debug("预览层关联的会话存在")

                    // 检查会话是否正在运行
                    if session.isRunning {
                        DebugLogger.debug("预览层关联的会话正在运行")
                    } else {
                        DebugLogger.warning("预览层关联的会话未运行")
                    }
                } else {
                    DebugLogger.error("预览层没有关联会话！")
                }

                // 检查连接状态
                if let connection = newLayer.connection {
                    DebugLogger.debug("预览层连接状态: enabled=\(connection.isEnabled)")

                    // 尝试启用连接
                    connection.isEnabled = true
                } else {
                    DebugLogger.warning("预览层没有连接")
                }
            } else {
                DebugLogger.debug("预览层被设置为nil")
            }
        }
    }

    // 视图布局改变时调用
    override func layoutSubviews() {
        super.layoutSubviews()
        DebugLogger.verbose("CameraPreviewView.layoutSubviews 被调用，bounds=\(bounds)")

        // 更新预览层框架以匹配视图边界
        CATransaction.begin()
        CATransaction.setDisableActions(true) // 禁用动画
        previewLayer?.frame = bounds
        CATransaction.commit()

        // 确保预览层仍在视图层次结构中
        if let layer = previewLayer, layer.superlayer == nil {
            DebugLogger.warning("预览层已从层次结构中移除，重新添加")
            self.layer.addSublayer(layer)
        }
    }

    // 视图即将从视图层次结构中移除时调用
    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)

        if newSuperview == nil {
            DebugLogger.debug("CameraPreviewView将从视图层次结构中移除")
        } else {
            DebugLogger.debug("CameraPreviewView将添加到新的父视图")
        }
    }
}

// SwiftUI视图代表 - 包装CameraPreviewView
struct CameraPreview: UIViewRepresentable {
    var previewLayer: AVCaptureVideoPreviewLayer

    // 创建UIView
    func makeUIView(context: Context) -> CameraPreviewView {
        DebugLogger.debug("CameraPreview.makeUIView被调用")

        let view = CameraPreviewView()
        view.backgroundColor = .black

        // 设置预览层
        view.previewLayer = previewLayer

        // 检查关联的会话
        DebugLogger.debug("预览层会话状态: \(previewLayer.session?.isRunning == true ? "正在运行" : "未运行")")

        return view
    }

    // 更新UIView
    func updateUIView(_ uiView: CameraPreviewView, context: Context) {
        DebugLogger.debug("CameraPreview.updateUIView被调用")

        // 检查预览层是否相同
        if uiView.previewLayer !== previewLayer {
            DebugLogger.debug("预览层实例已更改，更新UIView")
            uiView.previewLayer = previewLayer
        } else {
            DebugLogger.verbose("预览层实例相同，无需更新")
        }

        // 无论如何都重新布局视图
        uiView.setNeedsLayout()
    }

    // 视图将从层次结构中移除时调用
    static func dismantleUIView(_ uiView: CameraPreviewView, coordinator: ()) {
        DebugLogger.debug("CameraPreview.dismantleUIView被调用")
        uiView.previewLayer = nil
    }
}

// SwiftUI视图代表 - 包装PoseOverlayView
struct PoseOverlayViewRepresentable: UIViewRepresentable {
    var poseOverlayView: PoseOverlayView

    func makeUIView(context: Context) -> PoseOverlayView {
        DebugLogger.debug("PoseOverlayViewRepresentable.makeUIView被调用")
        return poseOverlayView
    }

    func updateUIView(_ uiView: PoseOverlayView, context: Context) {
        DebugLogger.verbose("PoseOverlayViewRepresentable.updateUIView被调用")
        // PoseOverlayView 会自动更新，无需额外操作
    }

    static func dismantleUIView(_ uiView: PoseOverlayView, coordinator: ()) {
        DebugLogger.debug("PoseOverlayViewRepresentable.dismantleUIView被调用")
        uiView.clear()
    }
}

/// 底部设置面板 - 使用 sheet 和 presentationDetents 来实现
struct BottomSheetView: View {
    /// 控制面板采用的停靠位置
    enum DetentHeight {
        case small     // 小尺寸，约为屏幕高度的10%
        case medium    // 中等尺寸，约为屏幕高度的50%
        case large     // 大尺寸，约为屏幕高度的100%
    }

    /// 控制面板关闭的状态绑定
    @Binding var isPresented: Bool

    /// 当前选中的停靠位置
    @State private var selectedDetent: DetentHeight = .medium

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 拖动指示器
                    DragIndicator()
                        .padding(.vertical, 8)

                    // 设置内容区域
                    settingsContent
                }
                .padding(.horizontal)
            }
            .navigationTitle("设置选项")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 关闭按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        isPresented = false
                    }) {
                        Text("完成")
                            .fontWeight(.bold)
                    }
                }

                // 面板大小切换按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Button(action: { selectedDetent = .small }) {
                            Label("小", systemImage: selectedDetent == .small ? "checkmark" : "")
                        }

                        Button(action: { selectedDetent = .medium }) {
                            Label("中", systemImage: selectedDetent == .medium ? "checkmark" : "")
                        }

                        Button(action: { selectedDetent = .large }) {
                            Label("大", systemImage: selectedDetent == .large ? "checkmark" : "")
                        }
                    } label: {
                        Image(systemName: "arrow.up.and.down")
                    }
                }
            }
            .background(Color(UIColor.systemGroupedBackground))
        }
        .presentationDetents([
            .fraction(0.1),   // 小：10%屏幕高度
            .fraction(0.5),   // 中：50%屏幕高度
            .fraction(0.95)   // 大：95%屏幕高度
        ], selection: detentSelection)
        .presentationDragIndicator(.visible) // 显示系统拖动指示器
        .interactiveDismissDisabled() // 禁止交互式关闭（必须点击完成按钮）
    }

    /// 设置面板主要内容
    private var settingsContent: some View {
        VStack(spacing: 20) {
            // 动作识别设置区域
            settingsSection(title: "动作识别") {
                toggleItem(.enableRecognition, isOn: true)
                toggleItem(.showCounter, isOn: true)
            }

            // 通知设置区域
            settingsSection(title: "通知设置") {
                toggleItem(.soundAlert, isOn: false)
            }

            // 显示设置区域
            settingsSection(title: "显示设置") {
                toggleItem("显示网格线", isOn: false)
                toggleItem("自动保存", isOn: true)
                toggleItem("无屏蔽模式", isOn: false)
            }

            // 关于区域
            settingsSection(title: "关于") {
                HStack {
                    Text("版本")
                    Spacer()
                    Text("1.0.0")
                        .foregroundColor(.gray)
                }
                .padding(.vertical, 8)
            }

            // 底部空白，确保内容可滚动
            Spacer(minLength: 40)
        }
    }

    /// 创建设置区域
    private func settingsSection<Content: View>(title: String, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.headline)
                .padding(.leading, 5)

            VStack(spacing: 0) {
                content()
            }
            .padding(.vertical, 5)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    /// 创建开关项
    private func toggleItem(_ title: String, isOn: Bool) -> some View {
        Toggle(LocalizedStringKey(title), isOn: .constant(isOn))
            .toggleStyle(SwitchToggleStyle(tint: .blue))
            .padding(.vertical, 8)
            .padding(.horizontal, 15)
    }

    /// 将选择的停靠高度转换为绑定值
    private var detentSelection: Binding<PresentationDetent> {
        Binding<PresentationDetent> {
            switch selectedDetent {
            case .small: return .fraction(0.1)
            case .medium: return .fraction(0.5)
            case .large: return .fraction(0.95)
            }
        } set: { newValue in
            switch newValue {
            case .fraction(0.1): selectedDetent = .small
            case .fraction(0.5): selectedDetent = .medium
            case .fraction(0.95): selectedDetent = .large
            default: selectedDetent = .medium
            }
        }
    }
}

/// 自定义拖动指示器
struct DragIndicator: View {
    var body: some View {
        Capsule()
            .fill(Color.gray.opacity(0.5))
            .frame(width: 40, height: 5)
    }
}

// 主视图（兼容版本）
struct FirstDetailView: View {
    @Environment(\.dismiss) var dismiss
    @Environment(\.horizontalSizeClass) var horizontalSizeClass

    // 支持两种初始化方式以保持向后兼容
    var exerciseName: String
    var exerciseType: ExerciseType?

    // 状态管理
    @StateObject private var cameraManager = CameraManager()
    @State private var showAlertMessage = false
    @State private var alertMessage = ""
    @State private var showSettings = false // 控制设置面板的显示

    // 兼容旧版本的初始化方法
    init(exerciseName: String) {
        self.exerciseName = exerciseName
        self.exerciseType = ExerciseType(rawValue: exerciseName)
        DebugLogger.info("FirstDetailView初始化（兼容模式），exerciseName=\(exerciseName)")
    }

    // 新版本的初始化方法
    init(exerciseType: ExerciseType) {
        self.exerciseType = exerciseType
        self.exerciseName = NSLocalizedString(exerciseType.rawValue, comment: "")
        DebugLogger.info("FirstDetailView初始化（新模式），exerciseType=\(exerciseType)")
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景部分：相机预览或黑色背景
                if let previewLayer = cameraManager.preview {
                    // 显示相机预览
                    ZStack {
                        CameraPreview(previewLayer: previewLayer)
                            .edgesIgnoringSafeArea(.all)
                            .onAppear {
                                DebugLogger.debug("CameraPreview视图出现")
                            }
                            .onDisappear {
                                DebugLogger.debug("CameraPreview视图消失")
                            }

                        // 叠加人体关键点绘制视图
                        if let poseOverlayView = cameraManager.poseOverlayView {
                            PoseOverlayViewRepresentable(poseOverlayView: poseOverlayView)
                                .edgesIgnoringSafeArea(.all)
                                .allowsHitTesting(false) // 不拦截触摸事件
                        }
                    }
                } else {
                    // 显示黑色背景和状态信息
                    Color.black.edgesIgnoringSafeArea(.all)
                        .overlay(
                            VStack(spacing: 20) {
                                Text(verbatim: .cameraInitializing)
                                    .font(.title)
                                    .foregroundColor(.white)
                                    .padding()

                                // 显示详细状态
                                Text(cameraManager.debugState)
                                    .foregroundColor(.yellow)
                                    .padding()
                                    .background(Color.black.opacity(0.5))
                                    .cornerRadius(10)

                                // 显示错误信息（如果有）
                                if let error = cameraManager.sessionError {
                                    Text(verbatim: .errorPrefix + error)
                                        .foregroundColor(.red)
                                        .multilineTextAlignment(.center)
                                        .padding()
                                        .background(Color.black.opacity(0.5))
                                        .cornerRadius(10)
                                }

                                // 检查会话状态
                                if cameraManager.preview != nil && !cameraManager.isSessionRunning {
                                    Button(action: {
                                        DebugLogger.debug("用户点击尝试重新启动相机")
                                        cameraManager.startSession()
                                    }) {
                                        Text(verbatim: .restartCamera)
                                            .foregroundColor(.white)
                                            .padding()
                                            .background(Color.blue)
                                            .cornerRadius(10)
                                    }
                                }

                                // 调试按钮 - 在开发阶段使用
#if DEBUG
                                Button(action: {
                                    DebugLogger.debug("用户点击检查相机状态")
                                    cameraManager.checkCameraStatus()
                                }) {
                                    Text(verbatim: .checkCameraStatus)
                                        .foregroundColor(.white)
                                        .padding()
                                        .background(Color.green)
                                        .cornerRadius(10)
                                }
#endif
                            }
                        )
                }

                // 如果相机中断，显示提示信息
                if cameraManager.isSessionInterrupted {
                    Text(verbatim: .cameraInterrupted)
                        .font(.title)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(10)
                }

                // 主界面结构：顶部控制栏和底部设置面板
                VStack(spacing: 0) {
                    // 顶部区域
                    HStack {
                        Text(exerciseName)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding()

                        Spacer()

                        // 切换摄像头按钮
                        Button(action: {
                            DebugLogger.debug("用户点击切换摄像头")
                            cameraManager.toggleCamera()
                        }) {
                            Image(systemName: "camera.rotate.fill")
                                .font(.title)
                                .foregroundColor(.white)
                                .padding()
                        }
                    }
                    .background(Color.black.opacity(0.3))

                    // 中间区域 - 占满所有可用空间
                    Spacer()
                }
                .edgesIgnoringSafeArea(.bottom) // 关键修改：确保面板延伸到底部

                // 我们使用.sheet展示设置面板，不再需要半透明遮罩层
            }
            .ignoresSafeArea(.keyboard) // 忽略键盘安全区域
            .adaptToScreenOrientation(horizontalSizeClass: horizontalSizeClass)
        }
        // 底部可拖拽设置面板
        .sheet(isPresented: $showSettings) {
            NavigationView {
                VStack(spacing: 0) {
                    // 拖拽指示器
                    Capsule()
                        .fill(Color.gray.opacity(0.4))
                        .frame(width: 40, height: 5)
                        .padding(.top, 8)

                    // 设置内容
                    List {
                        Section {
                            Toggle(isOn: .constant(true)) {
                                Text("启用声音")
                            }
                            Toggle(isOn: .constant(false)) {
                                Text("显示计数器")
                            }
                            Button(action: {
                                // 重置设置
                            }) {
                                Text("重置设置")
                                    .foregroundColor(.red)
                            }
                        } header: {
                            Text("通用设置")
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                }
                .navigationTitle("设置")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            showSettings = false
                        }
                    }
                }
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
        .navigationTitle(exerciseName)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    DebugLogger.debug("用户点击返回按钮")
                    dismiss()
                } label: {
                    Text(verbatim: .backButton)
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button {
                    showSettings = true
                } label: {
                    Image(systemName: "slider.horizontal.3")
                }
            }
        }
        .onAppear {
            DebugLogger.info("FirstDetailView显示")
            cameraManager.setupAndStartSession()
        }
        .onDisappear {
            DebugLogger.info("FirstDetailView消失")
            cameraManager.stopSession()
        }
        .alert(isPresented: $showAlertMessage) {
            Alert(
                title: Text(verbatim: .cameraError),
                message: Text(alertMessage),
                dismissButton: .default(Text(verbatim: .okButton))
            )
        }
        .onChange(of: cameraManager.sessionError) { errorMessage in
            if let errorMessage = errorMessage {
                DebugLogger.error("收到相机错误: \(errorMessage)")
                alertMessage = errorMessage
                showAlertMessage = true
            }
        }
    }
}

// 用于处理屏幕方向变化的扩展视图修饰符
struct OrientationAdaptiveModifier: ViewModifier {
    let horizontalSizeClass: UserInterfaceSizeClass?

    func body(content: Content) -> some View {
        if horizontalSizeClass == .regular {
            // 横屏布局
            content
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            // 竖屏布局
            content
        }
    }
}

// 自定义圆角形状 - 支持指定特定角落为圆角
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

extension View {
    // 屏幕方向自适应修饰符
    func adaptToScreenOrientation(horizontalSizeClass: UserInterfaceSizeClass?) -> some View {
        self.modifier(OrientationAdaptiveModifier(horizontalSizeClass: horizontalSizeClass))
    }

    // 自定义圆角修饰符 - 支持指定特定角落
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

// Extension to convert AVLayerVideoGravity to UIView.ContentMode
extension AVLayerVideoGravity {
    var contentMode: UIView.ContentMode {
        switch self {
        case .resizeAspectFill:
            return .scaleAspectFill
        case .resizeAspect:
            return .scaleAspectFit
        case .resize:
            return .scaleToFill
        default:
            return .scaleAspectFill
        }
    }
}

// 本地化字符串
extension String {
    // 使用 SwiftUI 的本地化字符串键值
    static let cameraToggle = "切换摄像头"
    static let settingsTitle = "设置选项"
    static let enableRecognition = "启用动作识别"
    static let showCounter = "显示计数"
    static let soundAlert = "声音提示"

    // 新增的本地化键值
    static let cameraError = "相机错误"
    static let okButton = "确定"
    static let backButton = "返回"
    static let cameraInitializing = "摄像头初始化中..."
    static let cameraInterrupted = "相机访问已中断"
    static let errorPrefix = "错误: "
    static let restartCamera = "重新启动相机"
    static let checkCameraStatus = "检查相机状态"
}

struct FirstDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            FirstDetailView(exerciseName: "仰卧起坐")
        }
        .environment(\.locale, .init(identifier: "zh-Hans"))
    }
}
