/**
 * 运动设置视图
 * 
 * 功能说明：
 * - 提供运动参数配置界面
 * - 支持实时预览设置效果
 * - 保存和恢复用户设置
 * 
 * 作者：FitCount Team
 * 创建时间：2024
 */

import SwiftUI

// MARK: - 运动设置视图
/**
 * 运动设置视图
 * 
 * 允许用户自定义运动检测参数
 */
struct ExerciseSettingsView: View {
    // MARK: - Properties
    let exerciseType: ExerciseType
    let onConfigurationChanged: (ExerciseConfiguration) -> Void
    
    // MARK: - State
    @State private var configuration: ExerciseConfiguration
    @State private var showResetAlert = false
    
    // MARK: - Initialization
    /**
     * 初始化设置视图
     * 
     * - Parameter exerciseType: 运动类型
     * - Parameter configuration: 当前配置
     * - Parameter onConfigurationChanged: 配置变化回调
     */
    init(
        exerciseType: ExerciseType,
        configuration: ExerciseConfiguration,
        onConfigurationChanged: @escaping (ExerciseConfiguration) -> Void
    ) {
        self.exerciseType = exerciseType
        self.onConfigurationChanged = onConfigurationChanged
        self._configuration = State(initialValue: configuration)
    }
    
    // MARK: - Body
    var body: some View {
        List {
            // 运动信息部分
            exerciseInfoSection
            
            // 检测参数部分
            detectionParametersSection
            
            // 反馈设置部分
            feedbackSettingsSection
            
            // 高级设置部分
            advancedSettingsSection
            
            // 操作按钮部分
            actionButtonsSection
        }
        .listStyle(InsetGroupedListStyle())
        .onChange(of: configuration) { newConfiguration in
            onConfigurationChanged(newConfiguration)
        }
    }
    
    // MARK: - View Sections
    
    /// 运动信息部分
    private var exerciseInfoSection: some View {
        Section {
            HStack {
                Image(systemName: exerciseType.iconName)
                    .font(.title2)
                    .foregroundColor(.blue)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(exerciseType.localizedName)
                        .font(.headline)
                    
                    Text(exerciseType.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        } header: {
            Text("运动信息")
        }
    }
    
    /// 检测参数部分
    private var detectionParametersSection: some View {
        Section {
            // 角度阈值设置
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("角度阈值")
                    Spacer()
                    Text("\(Int(configuration.angleThreshold))°")
                        .foregroundColor(.secondary)
                        .monospacedDigit()
                }
                
                Slider(
                    value: Binding(
                        get: { configuration.angleThreshold },
                        set: { newValue in
                            configuration = ExerciseConfiguration(
                                exerciseType: configuration.exerciseType,
                                angleThreshold: newValue,
                                minDetectionInterval: configuration.minDetectionInterval,
                                confidenceThreshold: configuration.confidenceThreshold,
                                enableSoundFeedback: configuration.enableSoundFeedback,
                                enableHapticFeedback: configuration.enableHapticFeedback
                            )
                        }
                    ),
                    in: 60...180,
                    step: 5
                )
                .accentColor(.blue)
                
                Text(angleThresholdDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
            
            // 检测间隔设置
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("检测间隔")
                    Spacer()
                    Text(String(format: "%.1f秒", configuration.minDetectionInterval))
                        .foregroundColor(.secondary)
                        .monospacedDigit()
                }
                
                Slider(
                    value: Binding(
                        get: { configuration.minDetectionInterval },
                        set: { newValue in
                            configuration = ExerciseConfiguration(
                                exerciseType: configuration.exerciseType,
                                angleThreshold: configuration.angleThreshold,
                                minDetectionInterval: newValue,
                                confidenceThreshold: configuration.confidenceThreshold,
                                enableSoundFeedback: configuration.enableSoundFeedback,
                                enableHapticFeedback: configuration.enableHapticFeedback
                            )
                        }
                    ),
                    in: 0.1...2.0,
                    step: 0.1
                )
                .accentColor(.blue)
                
                Text("设置动作检测的最小时间间隔，避免误计数")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
            
            // 置信度阈值设置
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("置信度阈值")
                    Spacer()
                    Text(String(format: "%.2f", configuration.confidenceThreshold))
                        .foregroundColor(.secondary)
                        .monospacedDigit()
                }
                
                Slider(
                    value: Binding(
                        get: { Double(configuration.confidenceThreshold) },
                        set: { newValue in
                            configuration = ExerciseConfiguration(
                                exerciseType: configuration.exerciseType,
                                angleThreshold: configuration.angleThreshold,
                                minDetectionInterval: configuration.minDetectionInterval,
                                confidenceThreshold: Float(newValue),
                                enableSoundFeedback: configuration.enableSoundFeedback,
                                enableHapticFeedback: configuration.enableHapticFeedback
                            )
                        }
                    ),
                    in: 0.3...0.9,
                    step: 0.05
                )
                .accentColor(.blue)
                
                Text("设置姿态检测的置信度要求，值越高检测越严格")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
            
        } header: {
            Text("检测参数")
        }
    }
    
    /// 反馈设置部分
    private var feedbackSettingsSection: some View {
        Section {
            Toggle("声音反馈", isOn: Binding(
                get: { configuration.enableSoundFeedback },
                set: { newValue in
                    configuration = ExerciseConfiguration(
                        exerciseType: configuration.exerciseType,
                        angleThreshold: configuration.angleThreshold,
                        minDetectionInterval: configuration.minDetectionInterval,
                        confidenceThreshold: configuration.confidenceThreshold,
                        enableSoundFeedback: newValue,
                        enableHapticFeedback: configuration.enableHapticFeedback
                    )
                }
            ))
            
            Toggle("震动反馈", isOn: Binding(
                get: { configuration.enableHapticFeedback },
                set: { newValue in
                    configuration = ExerciseConfiguration(
                        exerciseType: configuration.exerciseType,
                        angleThreshold: configuration.angleThreshold,
                        minDetectionInterval: configuration.minDetectionInterval,
                        confidenceThreshold: configuration.confidenceThreshold,
                        enableSoundFeedback: configuration.enableSoundFeedback,
                        enableHapticFeedback: newValue
                    )
                }
            ))
        } header: {
            Text("反馈设置")
        } footer: {
            Text("启用反馈可以在完成动作时提供声音或震动提示")
        }
    }
    
    /// 高级设置部分
    private var advancedSettingsSection: some View {
        Section {
            NavigationLink("MediaPipe 设置") {
                MediaPipeSettingsView()
            }
            
            NavigationLink("相机设置") {
                CameraSettingsView()
            }
            
        } header: {
            Text("高级设置")
        }
    }
    
    /// 操作按钮部分
    private var actionButtonsSection: some View {
        Section {
            Button(action: {
                showResetAlert = true
            }) {
                HStack {
                    Image(systemName: "arrow.counterclockwise")
                    Text("恢复默认设置")
                }
                .foregroundColor(.red)
            }
        }
        .alert("恢复默认设置", isPresented: $showResetAlert) {
            Button("取消", role: .cancel) { }
            Button("确认", role: .destructive) {
                resetToDefaults()
            }
        } message: {
            Text("这将恢复所有设置到默认值，是否继续？")
        }
    }
    
    // MARK: - Computed Properties
    
    /// 角度阈值描述
    private var angleThresholdDescription: String {
        switch exerciseType {
        case .situp:
            return "设置仰卧起坐的躯干角度阈值，角度越小要求起身越高"
        case .pullup:
            return "设置引体向上的手臂角度阈值，角度越小要求拉起越高"
        case .pushup:
            return "设置俯卧撑的手臂角度阈值，角度越小要求下压越低"
        case .squat:
            return "设置深蹲的腿部角度阈值，角度越小要求蹲得越低"
        case .plank:
            return "设置平板支撑的身体角度阈值，角度越接近180度要求越严格"
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 恢复默认设置
     */
    private func resetToDefaults() {
        configuration = ExerciseConfiguration.defaultConfiguration(for: exerciseType)
    }
}

// MARK: - MediaPipe 设置视图
/**
 * MediaPipe 设置视图
 */
struct MediaPipeSettingsView: View {
    var body: some View {
        List {
            Section {
                Text("MediaPipe 相关设置将在此处显示")
                    .foregroundColor(.secondary)
            } header: {
                Text("模型设置")
            }
        }
        .navigationTitle("MediaPipe 设置")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 相机设置视图
/**
 * 相机设置视图
 */
struct CameraSettingsView: View {
    var body: some View {
        List {
            Section {
                Text("相机相关设置将在此处显示")
                    .foregroundColor(.secondary)
            } header: {
                Text("相机配置")
            }
        }
        .navigationTitle("相机设置")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 预览
struct ExerciseSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ExerciseSettingsView(
                exerciseType: .situp,
                configuration: ExerciseConfiguration.defaultConfiguration(for: .situp)
            ) { _ in }
        }
    }
}
