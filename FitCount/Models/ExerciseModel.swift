/**
 * 运动类型和数据模型
 *
 * 功能说明：
 * - 定义支持的运动类型枚举
 * - 运动数据模型和配置
 * - 运动状态管理
 *
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import SwiftUI

// MARK: - 运动类型枚举
/**
 * 支持的运动类型
 *
 * 当前支持的运动：
 * - situp: 仰卧起坐
 * - pullup: 引体向上
 * - pushup: 俯卧撑
 * - squat: 深蹲
 * - plank: 平板支撑
 */
enum ExerciseType: String, CaseIterable, Identifiable {
    case situp = "exercise.situp"
    case pullup = "exercise.pullup"
    case pushup = "exercise.pushup"
    case squat = "exercise.squat"
    case plank = "exercise.plank"

    var id: String { rawValue }

    /// 运动的本地化显示名称
    var localizedName: LocalizedStringKey {
        LocalizedStringKey(rawValue)
    }

    /// 运动的描述信息
    var description: String {
        switch self {
        case .situp:
            return "通过检测躯干角度变化来计数仰卧起坐动作"
        case .pullup:
            return "通过检测手臂和身体位置变化来计数引体向上动作"
        case .pushup:
            return "通过检测手臂弯曲和身体高度变化来计数俯卧撑动作"
        case .squat:
            return "通过检测膝盖角度变化来计数深蹲动作"
        case .plank:
            return "检测平板支撑姿态的稳定性和持续时间"
        }
    }

    /// 运动的图标名称
    var iconName: String {
        switch self {
        case .situp:
            return "figure.core.training"
        case .pullup:
            return "figure.strengthtraining.traditional"
        case .pushup:
            return "figure.arms.open"
        case .squat:
            return "figure.squat"
        case .plank:
            return "figure.flexibility"
        }
    }
}

// MARK: - 运动配置模型
/**
 * 运动配置参数
 *
 * 包含每种运动的检测参数和阈值设置
 */
struct ExerciseConfiguration: Equatable {
    /// 运动类型
    let exerciseType: ExerciseType

    /// 角度检测阈值（度）
    let angleThreshold: Double

    /// 最小检测间隔（秒）
    let minDetectionInterval: TimeInterval

    /// 置信度阈值
    let confidenceThreshold: Float

    /// 是否启用声音反馈
    let enableSoundFeedback: Bool

    /// 是否启用震动反馈
    let enableHapticFeedback: Bool

    /// 默认配置
    static func defaultConfiguration(for exerciseType: ExerciseType) -> ExerciseConfiguration {
        switch exerciseType {
        case .situp:
            return ExerciseConfiguration(
                exerciseType: exerciseType,
                angleThreshold: 130.0,
                minDetectionInterval: 0.5,
                confidenceThreshold: 0.7,
                enableSoundFeedback: true,
                enableHapticFeedback: true
            )
        case .pullup:
            return ExerciseConfiguration(
                exerciseType: exerciseType,
                angleThreshold: 160.0,
                minDetectionInterval: 1.0,
                confidenceThreshold: 0.8,
                enableSoundFeedback: true,
                enableHapticFeedback: true
            )
        case .pushup:
            return ExerciseConfiguration(
                exerciseType: exerciseType,
                angleThreshold: 90.0,
                minDetectionInterval: 0.8,
                confidenceThreshold: 0.7,
                enableSoundFeedback: true,
                enableHapticFeedback: true
            )
        case .squat:
            return ExerciseConfiguration(
                exerciseType: exerciseType,
                angleThreshold: 90.0,
                minDetectionInterval: 1.0,
                confidenceThreshold: 0.8,
                enableSoundFeedback: true,
                enableHapticFeedback: true
            )
        case .plank:
            return ExerciseConfiguration(
                exerciseType: exerciseType,
                angleThreshold: 180.0,
                minDetectionInterval: 1.0,
                confidenceThreshold: 0.9,
                enableSoundFeedback: false,
                enableHapticFeedback: false
            )
        }
    }
}

// MARK: - 运动状态枚举
/**
 * 运动检测状态
 */
enum ExerciseState {
    case idle           // 空闲状态
    case detecting      // 检测中
    case counting       // 计数中
    case paused         // 暂停
    case completed      // 完成
}

// MARK: - 运动数据模型
/**
 * 运动会话数据模型
 *
 * 记录一次运动会话的所有数据
 */
struct ExerciseSession {
    /// 会话唯一标识
    let id: UUID

    /// 运动类型
    let exerciseType: ExerciseType

    /// 开始时间
    let startTime: Date

    /// 结束时间
    var endTime: Date?

    /// 当前计数
    var currentCount: Int

    /// 目标计数（可选）
    var targetCount: Int?

    /// 运动状态
    var state: ExerciseState

    /// 会话持续时间
    var duration: TimeInterval {
        let end = endTime ?? Date()
        return end.timeIntervalSince(startTime)
    }

    /// 是否已完成
    var isCompleted: Bool {
        state == .completed
    }

    /// 初始化新的运动会话
    init(exerciseType: ExerciseType, targetCount: Int? = nil) {
        self.id = UUID()
        self.exerciseType = exerciseType
        self.startTime = Date()
        self.endTime = nil
        self.currentCount = 0
        self.targetCount = targetCount
        self.state = .idle
    }
}

// MARK: - 运动项目模型（兼容现有代码）
/**
 * 运动项目模型
 *
 * 用于UI显示的运动项目，兼容现有的 ExerciseItem
 */
struct ExerciseItem: Identifiable {
    var id = UUID()
    var name: String
    var exerciseType: ExerciseType

    /// 从运动类型创建运动项目
    init(exerciseType: ExerciseType) {
        self.exerciseType = exerciseType
        self.name = exerciseType.rawValue
    }

    /// 兼容现有代码的初始化方法
    init(name: String) {
        self.name = name
        // 尝试从名称推断运动类型
        self.exerciseType = ExerciseType(rawValue: name) ?? .situp
    }
}
