/**
 * 人体姿态检测结果模型
 * 
 * 功能说明：
 * - 封装MediaPipe检测结果
 * - 提供姿态分析的数据结构
 * - 支持关键点坐标转换和计算
 * 
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import CoreGraphics
import MediaPipeTasksVision

// MARK: - 人体关键点枚举
/**
 * MediaPipe人体关键点索引枚举
 * 
 * 基于MediaPipe Pose Landmarker的33个关键点
 * 详细说明每个关键点的位置和用途
 */
enum PoseLandmarkIndex: Int, CaseIterable {
    // 面部关键点
    case nose = 0                    // 鼻子
    case leftEyeInner = 1           // 左眼内角
    case leftEye = 2                // 左眼
    case leftEyeOuter = 3           // 左眼外角
    case rightEyeInner = 4          // 右眼内角
    case rightEye = 5               // 右眼
    case rightEyeOuter = 6          // 右眼外角
    case leftEar = 7                // 左耳
    case rightEar = 8               // 右耳
    case mouthLeft = 9              // 嘴角左
    case mouthRight = 10            // 嘴角右
    
    // 上身关键点
    case leftShoulder = 11          // 左肩
    case rightShoulder = 12         // 右肩
    case leftElbow = 13             // 左肘
    case rightElbow = 14            // 右肘
    case leftWrist = 15             // 左手腕
    case rightWrist = 16            // 右手腕
    case leftPinky = 17             // 左小指
    case rightPinky = 18            // 右小指
    case leftIndex = 19             // 左食指
    case rightIndex = 20            // 右食指
    case leftThumb = 21             // 左拇指
    case rightThumb = 22            // 右拇指
    
    // 下身关键点
    case leftHip = 23               // 左髋
    case rightHip = 24              // 右髋
    case leftKnee = 25              // 左膝
    case rightKnee = 26             // 右膝
    case leftAnkle = 27             // 左踝
    case rightAnkle = 28            // 右踝
    case leftHeel = 29              // 左脚跟
    case rightHeel = 30             // 右脚跟
    case leftFootIndex = 31         // 左脚趾
    case rightFootIndex = 32        // 右脚趾
    
    /// 关键点的中文名称
    var chineseName: String {
        switch self {
        case .nose: return "鼻子"
        case .leftEyeInner: return "左眼内角"
        case .leftEye: return "左眼"
        case .leftEyeOuter: return "左眼外角"
        case .rightEyeInner: return "右眼内角"
        case .rightEye: return "右眼"
        case .rightEyeOuter: return "右眼外角"
        case .leftEar: return "左耳"
        case .rightEar: return "右耳"
        case .mouthLeft: return "嘴角左"
        case .mouthRight: return "嘴角右"
        case .leftShoulder: return "左肩"
        case .rightShoulder: return "右肩"
        case .leftElbow: return "左肘"
        case .rightElbow: return "右肘"
        case .leftWrist: return "左手腕"
        case .rightWrist: return "右手腕"
        case .leftPinky: return "左小指"
        case .rightPinky: return "右小指"
        case .leftIndex: return "左食指"
        case .rightIndex: return "右食指"
        case .leftThumb: return "左拇指"
        case .rightThumb: return "右拇指"
        case .leftHip: return "左髋"
        case .rightHip: return "右髋"
        case .leftKnee: return "左膝"
        case .rightKnee: return "右膝"
        case .leftAnkle: return "左踝"
        case .rightAnkle: return "右踝"
        case .leftHeel: return "左脚跟"
        case .rightHeel: return "右脚跟"
        case .leftFootIndex: return "左脚趾"
        case .rightFootIndex: return "右脚趾"
        }
    }
}

// MARK: - 姿态检测结果
/**
 * 姿态检测结果封装类
 * 
 * 将MediaPipe的原始检测结果封装为更易用的数据结构
 */
struct PoseDetectionResult {
    /// 检测时间戳
    let timestamp: TimeInterval
    
    /// 推理耗时（毫秒）
    let inferenceTime: Double
    
    /// 归一化关键点坐标（0-1范围）
    let normalizedLandmarks: [NormalizedLandmark]
    
    /// 世界坐标系关键点（米为单位）
    let worldLandmarks: [Landmark]?
    
    /// 检测置信度
    let confidence: Float
    
    /// 是否检测到有效姿态
    var isValidPose: Bool {
        return !normalizedLandmarks.isEmpty && confidence > 0.5
    }
    
    /// 从MediaPipe结果创建检测结果
    init(from result: ResultBundle) {
        self.timestamp = Date().timeIntervalSince1970
        self.inferenceTime = result.inferenceTime
        
        if let poseLandmarkerResult = result.poseLandmarkerResults.first as? PoseLandmarkerResult,
           !poseLandmarkerResult.landmarks.isEmpty {
            self.normalizedLandmarks = poseLandmarkerResult.landmarks[0]
            self.worldLandmarks = poseLandmarkerResult.worldLandmarks.isEmpty ? nil : poseLandmarkerResult.worldLandmarks[0]
            // 计算平均置信度作为整体置信度
            self.confidence = normalizedLandmarks.reduce(0) { $0 + $1.visibility } / Float(normalizedLandmarks.count)
        } else {
            self.normalizedLandmarks = []
            self.worldLandmarks = nil
            self.confidence = 0.0
        }
    }
    
    /// 获取指定关键点的归一化坐标
    func getNormalizedLandmark(_ index: PoseLandmarkIndex) -> NormalizedLandmark? {
        guard index.rawValue < normalizedLandmarks.count else { return nil }
        return normalizedLandmarks[index.rawValue]
    }
    
    /// 获取指定关键点的世界坐标
    func getWorldLandmark(_ index: PoseLandmarkIndex) -> Landmark? {
        guard let worldLandmarks = worldLandmarks,
              index.rawValue < worldLandmarks.count else { return nil }
        return worldLandmarks[index.rawValue]
    }
    
    /// 计算两个关键点之间的角度（使用归一化坐标）
    func calculateAngle(point1: PoseLandmarkIndex, vertex: PoseLandmarkIndex, point2: PoseLandmarkIndex) -> Double? {
        guard let p1 = getNormalizedLandmark(point1),
              let v = getNormalizedLandmark(vertex),
              let p2 = getNormalizedLandmark(point2) else {
            return nil
        }
        
        return calculateAngle(
            point1: CGPoint(x: CGFloat(p1.x), y: CGFloat(p1.y)),
            vertex: CGPoint(x: CGFloat(v.x), y: CGFloat(v.y)),
            point2: CGPoint(x: CGFloat(p2.x), y: CGFloat(p2.y))
        )
    }
    
    /// 计算两个关键点之间的角度（使用世界坐标）
    func calculateAngleInWorldSpace(point1: PoseLandmarkIndex, vertex: PoseLandmarkIndex, point2: PoseLandmarkIndex) -> Double? {
        guard let p1 = getWorldLandmark(point1),
              let v = getWorldLandmark(vertex),
              let p2 = getWorldLandmark(point2) else {
            return nil
        }
        
        return calculateAngle(
            point1: CGPoint(x: CGFloat(p1.x), y: CGFloat(p1.y)),
            vertex: CGPoint(x: CGFloat(v.x), y: CGFloat(v.y)),
            point2: CGPoint(x: CGFloat(p2.x), y: CGFloat(p2.y))
        )
    }
    
    /// 计算三点角度的通用方法
    private func calculateAngle(point1: CGPoint, vertex: CGPoint, point2: CGPoint) -> Double {
        let vector1 = CGPoint(x: point1.x - vertex.x, y: point1.y - vertex.y)
        let vector2 = CGPoint(x: point2.x - vertex.x, y: point2.y - vertex.y)
        
        let dotProduct = vector1.x * vector2.x + vector1.y * vector2.y
        let magnitude1 = sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
        let magnitude2 = sqrt(vector2.x * vector2.x + vector2.y * vector2.y)
        
        guard magnitude1 > 0 && magnitude2 > 0 else { return 0 }
        
        let cosAngle = dotProduct / (magnitude1 * magnitude2)
        let clampedCosAngle = max(-1.0, min(1.0, cosAngle))
        let angleInRadians = acos(clampedCosAngle)
        
        return angleInRadians * 180.0 / .pi
    }
    
    /// 获取身体中心点（肩膀和髋部的中点）
    func getBodyCenter() -> CGPoint? {
        guard let leftShoulder = getNormalizedLandmark(.leftShoulder),
              let rightShoulder = getNormalizedLandmark(.rightShoulder),
              let leftHip = getNormalizedLandmark(.leftHip),
              let rightHip = getNormalizedLandmark(.rightHip) else {
            return nil
        }
        
        let shoulderCenter = CGPoint(
            x: CGFloat(leftShoulder.x + rightShoulder.x) / 2,
            y: CGFloat(leftShoulder.y + rightShoulder.y) / 2
        )
        
        let hipCenter = CGPoint(
            x: CGFloat(leftHip.x + rightHip.x) / 2,
            y: CGFloat(leftHip.y + rightHip.y) / 2
        )
        
        return CGPoint(
            x: (shoulderCenter.x + hipCenter.x) / 2,
            y: (shoulderCenter.y + hipCenter.y) / 2
        )
    }
}

// MARK: - 姿态分析结果
/**
 * 姿态分析结果
 * 
 * 包含对检测到的姿态进行分析后的结果
 */
struct PoseAnalysisResult {
    /// 原始检测结果
    let detectionResult: PoseDetectionResult
    
    /// 躯干角度（用于仰卧起坐检测）
    let torsoAngle: Double?
    
    /// 手臂角度（用于引体向上、俯卧撑检测）
    let leftArmAngle: Double?
    let rightArmAngle: Double?
    
    /// 腿部角度（用于深蹲检测）
    let leftLegAngle: Double?
    let rightLegAngle: Double?
    
    /// 身体稳定性评分（用于平板支撑检测）
    let stabilityScore: Double?
    
    /// 姿态质量评分（0-1）
    let poseQuality: Double
    
    /// 从检测结果创建分析结果
    init(from detectionResult: PoseDetectionResult) {
        self.detectionResult = detectionResult
        
        // 计算躯干角度（肩膀-髋部-膝盖）
        self.torsoAngle = detectionResult.calculateAngle(
            point1: .leftShoulder,
            vertex: .leftHip,
            point2: .leftKnee
        )
        
        // 计算手臂角度
        self.leftArmAngle = detectionResult.calculateAngle(
            point1: .leftShoulder,
            vertex: .leftElbow,
            point2: .leftWrist
        )
        
        self.rightArmAngle = detectionResult.calculateAngle(
            point1: .rightShoulder,
            vertex: .rightElbow,
            point2: .rightWrist
        )
        
        // 计算腿部角度
        self.leftLegAngle = detectionResult.calculateAngle(
            point1: .leftHip,
            vertex: .leftKnee,
            point2: .leftAnkle
        )
        
        self.rightLegAngle = detectionResult.calculateAngle(
            point1: .rightHip,
            vertex: .rightKnee,
            point2: .rightAnkle
        )
        
        // 计算稳定性评分（基于关键点的可见性和置信度）
        self.stabilityScore = detectionResult.confidence > 0.7 ? Double(detectionResult.confidence) : nil
        
        // 计算整体姿态质量
        self.poseQuality = Double(detectionResult.confidence)
    }
}
