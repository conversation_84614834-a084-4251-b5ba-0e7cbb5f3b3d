/**
 * 运动计数器协议和基础实现
 * 
 * 功能说明：
 * - 定义运动计数器的通用协议
 * - 提供基础的计数逻辑实现
 * - 支持不同运动类型的扩展
 * 
 * 作者：FitCount Team
 * 创建时间：2024
 */

import Foundation
import Combine

// MARK: - 运动计数器协议
/**
 * 运动计数器协议
 * 
 * 定义所有运动计数器必须实现的基本功能
 */
protocol ExerciseCounterProtocol: ObservableObject {
    /// 当前计数
    var currentCount: Int { get }
    
    /// 运动类型
    var exerciseType: ExerciseType { get }
    
    /// 运动配置
    var configuration: ExerciseConfiguration { get set }
    
    /// 当前运动状态
    var exerciseState: ExerciseState { get }
    
    /// 是否正在计数
    var isActive: Bool { get }
    
    /// 处理姿态分析结果
    func processPoseAnalysis(_ analysis: PoseAnalysisResult)
    
    /// 开始计数
    func startCounting()
    
    /// 停止计数
    func stopCounting()
    
    /// 重置计数
    func resetCount()
    
    /// 暂停计数
    func pauseCounting()
    
    /// 恢复计数
    func resumeCounting()
}

// MARK: - 基础运动计数器
/**
 * 基础运动计数器实现
 * 
 * 提供通用的计数逻辑和状态管理
 */
class BaseExerciseCounter: ObservableObject, ExerciseCounterProtocol {
    // MARK: - Published Properties
    @Published var currentCount: Int = 0
    @Published var exerciseState: ExerciseState = .idle
    @Published var lastDetectionTime: Date = Date()
    
    // MARK: - Properties
    let exerciseType: ExerciseType
    var configuration: ExerciseConfiguration
    
    /// 是否正在计数
    var isActive: Bool {
        exerciseState == .detecting || exerciseState == .counting
    }
    
    /// 上次动作状态（用于检测动作完成）
    private var lastActionState: ActionState = .down
    
    /// 动作状态枚举
    private enum ActionState {
        case up      // 动作上升阶段
        case down    // 动作下降阶段
    }
    
    // MARK: - Initialization
    /**
     * 初始化运动计数器
     * 
     * - Parameter exerciseType: 运动类型
     * - Parameter configuration: 运动配置（可选，使用默认配置）
     */
    init(exerciseType: ExerciseType, configuration: ExerciseConfiguration? = nil) {
        self.exerciseType = exerciseType
        self.configuration = configuration ?? ExerciseConfiguration.defaultConfiguration(for: exerciseType)
    }
    
    // MARK: - Public Methods
    /**
     * 处理姿态分析结果
     * 
     * 基础实现，子类应该重写此方法来实现具体的计数逻辑
     * 
     * - Parameter analysis: 姿态分析结果
     */
    func processPoseAnalysis(_ analysis: PoseAnalysisResult) {
        // 检查检测间隔
        let now = Date()
        guard now.timeIntervalSince(lastDetectionTime) >= configuration.minDetectionInterval else {
            return
        }
        
        // 检查姿态质量
        guard analysis.poseQuality >= Double(configuration.confidenceThreshold) else {
            return
        }
        
        // 更新检测时间
        lastDetectionTime = now
        
        // 子类应该重写此方法实现具体逻辑
        processSpecificExercise(analysis)
    }
    
    /**
     * 开始计数
     */
    func startCounting() {
        exerciseState = .detecting
        resetCount()
    }
    
    /**
     * 停止计数
     */
    func stopCounting() {
        exerciseState = .completed
    }
    
    /**
     * 重置计数
     */
    func resetCount() {
        currentCount = 0
        lastActionState = .down
        lastDetectionTime = Date()
    }
    
    /**
     * 暂停计数
     */
    func pauseCounting() {
        if exerciseState == .detecting || exerciseState == .counting {
            exerciseState = .paused
        }
    }
    
    /**
     * 恢复计数
     */
    func resumeCounting() {
        if exerciseState == .paused {
            exerciseState = .detecting
        }
    }
    
    // MARK: - Protected Methods
    /**
     * 处理具体运动的计数逻辑
     * 
     * 子类应该重写此方法来实现具体的运动检测逻辑
     * 
     * - Parameter analysis: 姿态分析结果
     */
    func processSpecificExercise(_ analysis: PoseAnalysisResult) {
        // 基础实现为空，子类需要重写
    }
    
    /**
     * 增加计数
     * 
     * 当检测到一个完整的运动动作时调用
     */
    func incrementCount() {
        currentCount += 1
        exerciseState = .counting
        
        // 触发反馈
        if configuration.enableHapticFeedback {
            triggerHapticFeedback()
        }
        
        if configuration.enableSoundFeedback {
            triggerSoundFeedback()
        }
    }
    
    /**
     * 检查角度是否超过阈值
     * 
     * - Parameter angle: 当前角度
     * - Parameter threshold: 阈值角度
     * - Parameter isAbove: 是否检查角度大于阈值
     * - Returns: 是否满足条件
     */
    func checkAngleThreshold(_ angle: Double?, threshold: Double, isAbove: Bool = true) -> Bool {
        guard let angle = angle else { return false }
        return isAbove ? angle > threshold : angle < threshold
    }
    
    // MARK: - Private Methods
    /**
     * 触发触觉反馈
     */
    private func triggerHapticFeedback() {
        #if os(iOS)
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        #endif
    }
    
    /**
     * 触发声音反馈
     */
    private func triggerSoundFeedback() {
        #if os(iOS)
        // 播放系统声音
        AudioServicesPlaySystemSound(1057) // 短促的提示音
        #endif
    }
}

// MARK: - 仰卧起坐计数器
/**
 * 仰卧起坐计数器
 * 
 * 通过检测躯干角度变化来计数仰卧起坐动作
 */
class SitUpCounter: BaseExerciseCounter {
    /// 是否处于起身状态
    private var isInUpPosition = false
    
    /**
     * 初始化仰卧起坐计数器
     */
    override init(exerciseType: ExerciseType = .situp, configuration: ExerciseConfiguration? = nil) {
        super.init(exerciseType: exerciseType, configuration: configuration)
    }
    
    /**
     * 处理仰卧起坐的计数逻辑
     * 
     * 检测躯干角度变化：
     * 1. 躺下状态：躯干角度 > 阈值
     * 2. 起身状态：躯干角度 < 阈值
     * 3. 完整动作：从躺下到起身再回到躺下
     */
    override func processSpecificExercise(_ analysis: PoseAnalysisResult) {
        guard let torsoAngle = analysis.torsoAngle else { return }
        
        let threshold = configuration.angleThreshold
        
        // 检测动作状态变化
        if !isInUpPosition && torsoAngle < threshold {
            // 从躺下状态转为起身状态
            isInUpPosition = true
            exerciseState = .counting
        } else if isInUpPosition && torsoAngle > threshold {
            // 从起身状态回到躺下状态，完成一次仰卧起坐
            isInUpPosition = false
            incrementCount()
        }
    }
    
    /**
     * 重置计数器状态
     */
    override func resetCount() {
        super.resetCount()
        isInUpPosition = false
    }
}

// MARK: - 导入必要的框架
#if os(iOS)
import UIKit
import AudioToolbox
#endif
