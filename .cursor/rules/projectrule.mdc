---
description: 
globs: 
alwaysApply: true
---
1.这是一个 IOS 工程
2.使用 cocopod管理依赖
3.使用 swiftui作为视图框架
4.使用主要开发语言是 swift
5.编译项目使用`xcodebuild`进行编译.
6.你需要调用编译命令检查是否有错误,如果有请修复.
7.编写swiftUI 的时候你需要完善对应的PreviewProvider
8.所有文案都需要定义在IOS 多语言规范文件下.
9.新增的代码需要丰富的注释
10.你总是说中文
11.引用字符串请使用比较新的 API 比如LocalizedStringKey
12.编译项目使用`xcodebuild -workspace FitCount.xcworkspace -scheme FitCount -destination 'platform=iOS Simulator,name=iPhone 16' clean build`进行编译.
13.项目使用https://ai.google.dev/edge/mediapipe/solutions/vision/pose_landmarker 作为姿势检测
14.姿势检测会返回 33 坐标点位置:
    0 - nose
    1 - left eye (inner)
    2 - left eye
    3 - left eye (outer)
    4 - right eye (inner)
    5 - right eye
    6 - right eye (outer)
    7 - left ear
    8 - right ear
    9 - mouth (left)
    10 - mouth (right)
    11 - left shoulder
    12 - right shoulder
    13 - left elbow
    14 - right elbow
    15 - left wrist
    16 - right wrist
    17 - left pinky
    18 - right pinky
    19 - left index
    20 - right index
    21 - left thumb
    22 - right thumb
    23 - left hip
    24 - right hip
    25 - left knee
    26 - right knee
    27 - left ankle
    28 - right ankle
    29 - left heel
    30 - right heel
    31 - left foot index
    32 - right foot index

15.使用pose_landmarker坐标请使用世界坐标,以避免旋转屏幕等带来的计算问题.
16.pose_landmarker文档: https://ai.google.dev/edge/mediapipe/solutions/vision/pose_landmarker/ios 
18.每次执行完命令后后需要执行编译检查是否有错误,如果有请修复,然后持续编译在修复到成功为止.
# 行动定义
当我说编译,编译项目时是指要求通过 `xcodebuild`编译项目,编译发现错误你需要修复后重新编译.
